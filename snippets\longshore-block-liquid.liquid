{{ block.settings.liquid }}

{% comment %}

{%- when 'liquid' -%}
  {% render 'custom-block-liquid', block: block %}

{
  "type": "liquid",
  "name": "Custon liquid",
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme_content",
      "label": "Block color scheme",
      "default": "background-1"
    },
    {
      "type": "liquid",
      "id": "liquid",
      "label": "Custom liquid"
    },
    {
      "type": "select",
      "id": "colomn_span_amount",
      "options": [
        {
          "value": "grid__item--span-1",
          "label": "1"
        },
        {
          "value": "grid__item--span-2",
          "label": "2"
        },
        {
          "value": "grid__item--span-3",
          "label": "3"
        }
      ],
      "default": "grid__item--span-1",
      "label": "Column span amount (mobile)"
    },
    {
      "type": "select",
      "id": "colomn_span_amount_tablet",
      "options": [
        {
          "value": "grid__item--span-1-tablet",
          "label": "1"
        },
        {
          "value": "grid__item--span-2-tablet",
          "label": "2"
        },
        {
          "value": "grid__item--span-3-tablet",
          "label": "3"
        },
        {
          "value": "grid__item--span-4-tablet",
          "label": "4"
        }
      ],
      "default": "grid__item--span-1-tablet",
      "label": "Column span (tablet only)",
      "info": "Screen (min-width: 750px) and (max-width: 989px)"
    },
    {
      "type": "select",
      "id": "colomn_span_amount_desktop",
      "options": [
        {
          "value": "grid__item--span-1-desktop",
          "label": "1"
        },
        {
          "value": "grid__item--span-2-desktop",
          "label": "2"
        },
        {
          "value": "grid__item--span-3-desktop",
          "label": "3"
        },
        {
          "value": "grid__item--span-4-desktop",
          "label": "4"
        },
        {
          "value": "grid__item--span-5-desktop",
          "label": "5"
        },
        {
          "value": "grid__item--span-6-desktop",
          "label": "6"
        }
      ],
      "default": "grid__item--span-1-desktop",
      "label": "Column span (desktop only)",
      "info": "Screen (min-width: 990px)"
    },
    {
      "type": "text",
      "id": "custom_classes",
      "label": "Block custom classes"
    }
  ]
}
{%  endcomment %}