/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "heading_size": "h2",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 16,
        "padding_top_desktop": 56,
        "padding_bottom_desktop": 16
      }
    },
    "image_banner_fcyQJt": {
      "type": "image-banner",
      "blocks": {
        "heading_KEHBUe": {
          "type": "heading",
          "settings": {
            "heading": "Upgrade Your Unwind",
            "heading_liquid": "",
            "heading_size": "h2"
          }
        }
      },
      "block_order": [
        "heading_KEHBUe"
      ],
      "name": "Hero",
      "settings": {
        "hide_size": "",
        "image": "shopify://shop_images/ig_NATEAPPEL_20231022_0742.jpg",
        "background_video": "",
        "image_height_desktop": "large",
        "banner_minheight_desktop": 75,
        "banner_minheight_desktop_units": "vh",
        "desktop_content_position": "bottom-center",
        "desktop_content_alignment": "center",
        "show_text_box": true,
        "buttons_bottom_desktop": false,
        "background_video_mobile": "",
        "image_behavior": "none",
        "image_height_mobile": "small",
        "banner_minheight_mobile": 75,
        "banner_minheight_mobile_units": "vh",
        "mobile_content_alignment": "center",
        "show_text_below": true,
        "reverse_text_placement_mobile": false,
        "buttons_bottom_mobile": false,
        "overlay_gradient": "",
        "image_overlay_opacity_mobile": 0,
        "image_overlay_opacity": 0,
        "color_scheme": "",
        "content_width": "full-width",
        "override_content_max_width": false,
        "content_max_width": 71,
        "content_max_width_desktop": 90,
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "color_scheme_section": "",
        "custom_css_class": "",
        "custom_liquid": "",
        "page": ""
      }
    },
    "image_with_text_qyYBpq": {
      "type": "image-with-text",
      "blocks": {
        "heading_YHGHTn": {
          "type": "heading",
          "settings": {
            "heading": "VIP Status, Everyday",
            "heading_liquid": "",
            "heading_size": "h3"
          }
        },
        "text_xM8HRV": {
          "type": "text",
          "settings": {
            "text": "<p><strong>Welcome to Shift Society - your all-access pass to Downshift.</strong></p><p>Subscribe today and get:</p><ul><li><strong>20% off + free shipping (orders $70+) on monthly orders</strong></li><li>Early access to <strong>new flavors + limited runs</strong></li><li>Exclusive <strong>merch + member-only discounts</strong></li><li>Share the love with <strong>friends + family perks</strong></li><li>Behind-the-scenes looks at the world of Downshift</li></ul><p>Shift Society isn't just a subscription, it's the inside track to enhancing your unwind.</p>",
            "text_style": "body"
          }
        }
      },
      "block_order": [
        "heading_YHGHTn",
        "text_xM8HRV"
      ],
      "name": "About",
      "settings": {
        "image": "shopify://shop_images/Downshift__jussioksanen-5562-min-2.jpg",
        "show_video_controls": false,
        "image_contain": false,
        "height": "adapt",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "adapt",
        "desktop_image_width": "medium",
        "layout": "image_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "",
        "image_behavior": "none",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 30,
        "padding_bottom_desktop": 30,
        "page": "",
        "hide_size": "",
        "color_scheme_section": "",
        "content_width": "page-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "image_with_text_tnD3ki": {
      "type": "image-with-text",
      "blocks": {
        "heading_LBbypK": {
          "type": "heading",
          "settings": {
            "heading": "+ FREE PUFFY CAN COOLER!",
            "heading_liquid": "",
            "heading_size": "h2"
          }
        },
        "text_MGrKeW": {
          "type": "text",
          "settings": {
            "text": "<p>When you join with any subscription. Sip in style.</p>",
            "text_style": "body"
          }
        }
      },
      "block_order": [
        "heading_LBbypK",
        "text_MGrKeW"
      ],
      "name": "t:sections.image-with-text.presets.name",
      "settings": {
        "image": "shopify://shop_images/Free_Puffy_Jacket_Can_Cooler_1.png",
        "show_video_controls": false,
        "image_contain": false,
        "height": "adapt",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "adapt",
        "desktop_image_width": "medium",
        "layout": "image_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "",
        "image_behavior": "none",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "page": "",
        "hide_size": "",
        "color_scheme_section": "",
        "content_width": "page-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "rich_text_MDatVN": {
      "type": "rich-text",
      "blocks": {
        "heading_7ytejW": {
          "type": "heading",
          "settings": {
            "heading": "Pick a Bundle...",
            "heading_size": "h1",
            "text_indent": ""
          }
        },
        "text_AHyf8P": {
          "type": "text",
          "settings": {
            "text": "<p>Not sure what to choose? Start with one of our prebuilt bundles, which include our most popular products. Then modify any part of your order once you decide what works best for you. </p>",
            "text_indent": ""
          }
        }
      },
      "block_order": [
        "heading_7ytejW",
        "text_AHyf8P"
      ],
      "name": "Bundle  Description",
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "center",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_top": 0,
        "padding_bottom_mobile": 30,
        "padding_bottom": 30,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "multicolumn_V3NjWj": {
      "type": "multicolumn",
      "blocks": {
        "column_RtNYd9": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/starter-bundle_9825c802-8869-4fab-b700-ec54a4fab348.png",
            "title": "Starter Bundle",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<ul>\n\t<li>4 cans DownShift Lime Citrus</li>\n\t<li>4 cans DownShift Trail Berry</li>\n\t<li>4 cans Shift Margarita</li>\n\t<li>10ct Bag DownShift Gummies Trail Berry</li>\n</ul>\n<br /><br /><br /><br /><br />\n<p><s>$89.98</s> -$18.00 (20%)</p>\n<p style=\"font-weight: bold; font-size: 2.15rem\">$71.98 + Free Shipping</p>\n<p>$4.60 / Can</p>\n<p>$1.60 / gummy</p>",
            "link_label": "",
            "link_label_liquid": "Add to Cart\n\n<script type=\"application/json\" id=\"buildabundle-addtocart-starter\">\n{\n    \"items\": [{\n        \"id\": \"46874711949557\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728005877\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874723713269\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874722599157\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        }    \n    ]\n}\n</script>",
            "link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9254644154613?product=shift-society-build-a-custom-bundle",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "scheme-1",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "column_BLKYjn": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/bestsellers-bundle_5797207d-f033-472a-8d22-410441910e48.png",
            "title": "Best Sellers Bundle",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<ul>\n\t<li>4 cans DownShift Lime Citrus</li>\n\t<li>4 cans DownShift Trail Berry</li>\n\t<li>4 cans Shift Margarita</li>\n\t<li>4 cans Shift Paloma</li>\n\t<li>10ct Bag DownShift Gummies Trail Berry</li>\n\t<li>10ct Bag DownShift Gummies Wild Tangerine</li>\n</ul>\n<br />\n<p><s>$133.30</s> -$26.66 (20%)</p>\n<p style=\"font-weight: bold; font-size: 2.15rem\">$106.64 + Free Shipping</p>\n<p>$4.60 / Can</p>\n<p>$1.60 / Gummy</p>",
            "link_label": "",
            "link_label_liquid": "Add to Cart\n\n<script type=\"application/json\" id=\"buildabundle-addtocart-bestsellers\">\n{\n    \"items\": [{\n        \"id\": \"46874706772213\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874711949557\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728136949\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728005877\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874723713269\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874722599157\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        }    \n    ]\n}\n</script>",
            "link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9254644154613?product=shift-society-build-a-custom-bundle",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "column_3CfcDa": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/ShiftBundleTransparent.png",
            "title": "All In Bundle",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<ul>\n    <li>4 cans DownShift Lime Citrus</li>\n    <li>4 cans DownShift Trail Berry</li>\n    <li>4 cans DownShift Wild Tangerine</li>\n    <li>4 cans Shift Margarita</li>\n    <li>4 cans Shift Paloma</li>\n    <li>4 cans Shift Ginger Sour</li>\n    <li>10ct Bag DownShift Gummies Trail Berry</li>\n    <li>10ct Bag DownShift Gummies Wild Tangerine</li>\n</ul>\n<br />\n<p><s>$179.96</s> -$35.99 (20%)</p>\n<p style=\"font-weight: bold; font-size: 2.15rem\">$143.97 + Free Shipping</p>\n<p>$4.60 / Can</p>\n<p>$1.60 / Gummy</p>",
            "link_label": "",
            "link_label_liquid": "Add to Cart\n\n<script type=\"application/json\" id=\"buildabundle-addtocart-allin\">\n{\n    \"items\": [{\n        \"id\": \"46874706772213\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874711949557\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874727842037\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728136949\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874728005877\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874726760693\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874723713269\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        },\n        {\n        \"id\": \"46874722599157\",\n        \"quantity\": 1,\n            \"properties\": {\n                \"_bundleUid\": \"1dbb2f90-7ebf-4d58-b69b-ce321a19f838\",\n                \"_bundleHandle\": \"shift-society-build-a-custom-bundle\",\n                \"_bundleType\": \"dynamicBaB\",\n                \"_bundleLabel\": \"Select at least 1 item\",\n                \"_bundleProductId\": \"gid://shopify/Product/9254644154613\",\n                \"_bundleName\": \"Shift Society Build a Custom Bundle\",\n                \"_bundleBoxSize\": 0,\n                \"_bundleBoxMinSize\": 3,\n                \"_variantId\": \"gid://shopify/ProductVariant/46874917175541\"\n            },\n            \"selling_plan\": \"4645290229\"\n        }\n    ]\n}\n</script>",
            "link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9254644154613?product=shift-society-build-a-custom-bundle",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "column_RtNYd9",
        "column_BLKYjn",
        "column_3CfcDa"
      ],
      "name": "Cards",
      "settings": {
        "hide_size": "",
        "color_scheme_section": "scheme-6",
        "color_scheme_content": "scheme-1",
        "background_style": "primary",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 3,
        "columns_desktop": 3,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 30,
        "padding_bottom_mobile": 30,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 60,
        "padding_bottom_desktop": 60,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "page": "",
        "custom_css_class": "buildabundle-addtocart",
        "custom_liquid": "<script>\n    // handle dynamic Add to Cart functionality\n    document.addEventListener('DOMContentLoaded', () => {\n        const bundleATCbtns = document.querySelectorAll('.buildabundle-addtocart .button');  \n        bundleATCbtns.forEach(thisButton => {\n            thisButton.addEventListener('click', function(event) {\n                event.preventDefault();\n                // find child <script> tag with ID starting with 'buildabundle-addtocart'\n                const bundleContents = thisButton.querySelector('script[id^=\"buildabundle-addtocart\"]');\n                if(bundleContents){\n                    const bundleJSON = JSON.parse(bundleContents.textContent);\n\n                    // add bundle to cart\n                    fetch(window.Shopify.routes.root + 'cart/add.js', {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify(bundleJSON)\n                    })\n                    .then(response => {\n                        window.location.href = '/cart'\n                        return response.json();\n                    })\n                    .catch((error) => {\n                        console.error('Error:', error);\n                    });\n                }\n            });\n        });\n    });\n</script>"
      }
    },
    "image_with_text_HFVkMj": {
      "type": "image-with-text",
      "blocks": {
        "heading_Ef9wqK": {
          "type": "heading",
          "settings": {
            "heading": "...or Build your own.",
            "heading_liquid": "",
            "heading_size": "h2"
          }
        },
        "text_qVm39f": {
          "type": "text",
          "settings": {
            "text": "<p>Want a tailored Downshift selection? Great! Subscribe to any quantity of any product (12 can minimum). Change, pause, or cancel at any time.</p>",
            "text_style": "body"
          }
        },
        "button_fPapqw": {
          "type": "button",
          "settings": {
            "button_label": "Get Started",
            "button_label_liquid": "",
            "button_link": "https://enjoyshift.com/tools/bundle-subscriptions/bundle/9254644154613?product=shift-society-build-a-custom-bundle&selling_plan=4645290229",
            "button_style": "button button--primary",
            "button_label_2": "",
            "button_label_2_liquid": "",
            "button_link_2": "",
            "button_style_2": "button button--secondary"
          }
        }
      },
      "block_order": [
        "heading_Ef9wqK",
        "text_qVm39f",
        "button_fPapqw"
      ],
      "custom_css": [
        "@media screen and (max-width: 767px) {.image-with-text__media-item {order: 1; }}"
      ],
      "name": "t:sections.image-with-text.presets.name",
      "settings": {
        "image": "shopify://shop_images/Build_a_box_clean.png",
        "show_video_controls": false,
        "image_contain": false,
        "height": "adapt",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "adapt",
        "desktop_image_width": "medium",
        "layout": "text_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "",
        "image_behavior": "none",
        "mobile_content_alignment": "center",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 50,
        "padding_bottom_desktop": 30,
        "page": "",
        "hide_size": "",
        "color_scheme_section": "",
        "content_width": "page-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "collapsible_content_LAcCJh": {
      "type": "collapsible-content",
      "blocks": {
        "collapsible_row_pVAHEV": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Lorem Ipsum 1",
            "icon": "none",
            "row_content": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_VYNqJL": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Lorem Ipsum 2",
            "icon": "none",
            "row_content": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_U8aRwt": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Lorem Ipsum 3",
            "icon": "none",
            "row_content": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "collapsible_row_bVHwfw": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Lorem Ipsum 4",
            "icon": "none",
            "row_content": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>",
            "row_content_liquid": "",
            "page": ""
          }
        }
      },
      "block_order": [
        "collapsible_row_pVAHEV",
        "collapsible_row_VYNqJL",
        "collapsible_row_U8aRwt",
        "collapsible_row_bVHwfw"
      ],
      "disabled": true,
      "name": "FAQs",
      "settings": {
        "caption": "",
        "heading": "Frequently Asked Questions",
        "heading_size": "h2",
        "heading_alignment": "center",
        "layout": "row",
        "container_color_scheme": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba",
        "color_scheme": "scheme-1",
        "open_first_collapsible_row": false,
        "image_ratio": "adapt",
        "desktop_layout": "image_second",
        "padding_top_desktop": 32,
        "padding_bottom_desktop": 0,
        "padding_top_mobile": 32,
        "padding_bottom_mobile": 0
      }
    },
    "17525410526b1bbc1c": {
      "type": "apps",
      "blocks": {
        "junip_junip_review_carousel_YC3zRe": {
          "type": "shopify://apps/junip/blocks/junip-review-carousel/dc14f5a8-ed15-41b1-ad08-cfba23f9789b",
          "settings": {
            "reviewsType": "product_reviews",
            "sortOrder": "highestRated",
            "product": "",
            "reviewTag": "",
            "showSummary": true,
            "title": "Reviews",
            "paddingTop": 48,
            "paddingBottom": 48,
            "containerClass": ""
          }
        }
      },
      "block_order": [
        "junip_junip_review_carousel_YC3zRe"
      ],
      "settings": {
        "include_margins": true,
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    }
  },
  "order": [
    "main",
    "image_banner_fcyQJt",
    "image_with_text_qyYBpq",
    "image_with_text_tnD3ki",
    "rich_text_MDatVN",
    "multicolumn_V3NjWj",
    "image_with_text_HFVkMj",
    "collapsible_content_LAcCJh",
    "17525410526b1bbc1c"
  ]
}
