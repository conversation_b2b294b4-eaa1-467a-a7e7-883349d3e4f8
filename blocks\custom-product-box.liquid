<div class="{{ block.settings.custom_css }}" {{ block.shopify_attributes }}>
  {% if block.settings.link %}
    <a href="{{ block.settings.link }}" class="tw:no-underline">
  {% endif %}
  <div class="color-{{ block.settings.color_scheme }} tw:p-6 tw:rounded-3xl">
    <h3>
      {{ block.settings.title_line_1 -}}
      {%- if block.settings.title_line_2 -%}
        <br>
        {{ block.settings.title_line_2 -}}
      {%- endif %}
    </h3>
    <h6>{{ block.settings.subtitle }}</h6>
    <div class=" tw:grid tw:grid-cols-3 tw:items-end tw:rounded-xl">
      <div class="color-scheme-2 tw:col-span-3 tw:md:col-span-2">
        {{ block.settings.text }}
      </div>
      {% if block.settings.image %}
      {{
        block.settings.image
        | image_url: width: 1000
        | image_tag: class: 'tw:w-full tw:h-auto tw:col-span-2 tw:md:col-span-1'
      }}
      {% endif %}
    </div>
  </div>
  {% if block.settings.link %}
    </a>
  {% endif %}
</div>

{% schema %}
{
  "name": "Product box",
  "tag": null,
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-2"
    },
    {
      "type": "url",
      "id": "link",
      "label": "Link"
    },
    {
      "type": "text",
      "id": "title_line_1",
      "label": "Title Line 1"
    },
    {
      "type": "text",
      "id": "title_line_2",
      "label": "Title line 2",
      "info": "Optional second line of title"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "Text"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "custom_css",
      "label": "Wrapper div additional CSS classes"
    }
  ],
  "presets": [{ "name": "Product box" }]
}
{% endschema %}
