{% comment %}
  bs-add
  - hide/show option added
  - max width for content mobile/desktop
  - slide height on mobile/desktop
  - added color scheme options with gradient option (section and content level)
  - added custom liquid block
  - added padding options mobile/desktop
  - ability to align for text mobile/desktop
  - added button scheme options
{% endcomment %}

{{ 'section-image-banner.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-slideshow.css' | asset_url | stylesheet_tag }}


{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
  
  {%- if section.settings.slide_height_mobile == 'adapt' and section.blocks.first.settings.image != blank -%}
    @media screen and (max-width: 749px) {
      #Slider-{{ section.id }}::before,
      #Slider-{{ section.id }} .media::before,
      #Slider-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
        padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }
  {% endif %}

  {%- if section.settings.slide_height_desktop == 'adapt' and section.blocks.first.settings.image != blank -%}
    @media screen and (min-width: 750px) {
      #Slider-{{ section.id }}::before,
      #Slider-{{ section.id }} .media::before {
        padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }
  {%- endif -%}

  {%- if section.settings.hide_pagination_borders -%}
    #shopify-section-{{ section.id }} slideshow-component .slider-buttons {
      border: none;
    }
  {%- endif -%}
{%- endstyle -%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="color-{{ section.settings.color_scheme }} gradient">
    <div class="section-{{ section.id }}-padding">
      <slideshow-component
        class="slider-mobile-gutter {{ section.settings.section_width }}{% if section.settings.show_text_below %} mobile-text-below{% endif %}"
        role="region"
        aria-roledescription="{{ 'sections.slideshow.carousel' | t }}"
        aria-label="{{ section.settings.accessibility_info | escape }}"
      >
        {%- if section.settings.auto_rotate and section.blocks.size > 1 -%}
          <div class="slideshow__controls slideshow__controls--top slider-buttons{% if section.settings.show_text_below %} slideshow__controls--border-radius-mobile{% endif %}">
            <button
              type="button"
              class="slider-button slider-button--prev"
              name="previous"
              aria-label="{{ 'sections.slideshow.previous_slideshow' | t }}"
              aria-controls="Slider-{{ section.id }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
            <div class="slider-counter slider-counter--{{ section.settings.slider_visual }}{% if section.settings.slider_visual == 'counter' or section.settings.slider_visual == 'numbers' %} caption{% endif %}">
              {%- if section.settings.slider_visual == 'counter' -%}
                <span class="slider-counter--current">1</span>
                <span aria-hidden="true"> / </span>
                <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                <span class="slider-counter--total">{{ section.blocks.size }}</span>
              {%- else -%}
                <div class="slideshow__control-wrapper">
                  {%- for block in section.blocks -%}
                    <button
                      class="slider-counter__link slider-counter__link--{{ section.settings.slider_visual }} link"
                      aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                      aria-controls="Slider-{{ section.id }}"
                    >
                      {%- if section.settings.slider_visual == 'numbers' -%}
                        {{ forloop.index -}}
                      {%- else -%}
                        <span class="dot"></span>
                      {%- endif -%}
                    </button>
                  {%- endfor -%}
                </div>
              {%- endif -%}
            </div>
            <button
              type="button"
              class="slider-button slider-button--next"
              name="next"
              aria-label="{{ 'sections.slideshow.next_slideshow' | t }}"
              aria-controls="Slider-{{ section.id }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
  
            {%- if section.settings.auto_rotate -%}
              <button
                type="button"
                class="slideshow__autoplay slider-button{% if section.settings.auto_rotate == false %} slideshow__autoplay--paused{% endif %}"
                aria-label="{{ 'sections.slideshow.pause_slideshow' | t }}"
              >
                <span class="svg-wrapper">
                  {{- 'icon-pause.svg' | inline_asset_content -}}
                </span>
                <span class="svg-wrapper">
                  {{- 'icon-play.svg' | inline_asset_content -}}
                </span>
              </button>
            {%- endif -%}
          </div>
        {%- endif -%}
  
        <div class="slideshow banner banner-desktop-{{ section.settings.slide_height_desktop }} banner-mobile-{{ section.settings.slide_height_mobile }} grid grid--1-col slider slider--everywhere{% if section.settings.show_text_below %} banner--mobile-bottom{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}"
          id="Slider-{{ section.id }}"
          aria-live="polite"
          aria-atomic="true"
          data-autoplay="{{ section.settings.auto_rotate }}"
          data-speed="{{ section.settings.change_slides_speed }}"
        >
          {%- for block in section.blocks -%}
            <style>
              #Slide-{{ section.id }}-{{ forloop.index }} .banner__media::after {
                opacity: {{ block.settings.image_overlay_opacity | divided_by: 100.0 }};
              }
  
              @media screen and (min-width: 750px) {
                #Slide-{{ section.id }}-{{ forloop.index }} .banner__box {
                  max-width: {{ block.settings.content_width_tablet }}rem;
                }
              }
  
              @media screen and (min-width: 990px) {
                #Slide-{{ section.id }}-{{ forloop.index }} .banner__box {
                  max-width: {{ block.settings.content_width_desktop }}rem;
                }
              }
            </style>
            <div
              class="slideshow__slide grid__item grid--1-col slider__slide"
              id="Slide-{{ section.id }}-{{ forloop.index }}"
              {{ block.shopify_attributes }}
              role="group"
              aria-roledescription="{{ 'sections.slideshow.slide' | t }}"
              aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
              tabindex="-1"
            >
              {%- if block.settings.image -%}
                <div class="slideshow__media banner__media media{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %}">
                  {%- liquid
                    assign height = block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round
                    if section.settings.image_behavior == 'ambient'
                      assign sizes = '120vw'
                      assign widths = '450, 660, 900, 1320, 1800, 2136, 2400, 3600, 7680'
                    else
                      assign sizes = '100vw'
                      assign widths = '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'
                    endif
                    assign fetch_priority = 'auto'
                    if section.index == 1
                      assign fetch_priority = 'high'
                    endif
                  -%}
                  {%- if forloop.first %}
                    {{
                      block.settings.image
                      | image_url: width: 3840
                      | image_tag: height: height, sizes: sizes, widths: widths, fetchpriority: fetch_priority
                    }}
                  {%- else -%}
                    {{
                      block.settings.image
                      | image_url: width: 3840
                      | image_tag: loading: 'lazy', height: height, sizes: sizes, widths: widths
                    }}
                  {%- endif -%}
                </div>
            {%- endif -%}
              <div class="slideshow__text-wrapper banner__content banner__content--{{ block.settings.box_align }} page-width{% if block.settings.show_text_box == false %} banner--desktop-transparent{% endif %}{% if settings.animations_reveal_on_scroll and forloop.index == 1 %} scroll-trigger animate--slide-in{% endif %}">
                <div class="slideshow__text banner__box content-container content-container--full-width-mobile color-{{ block.settings.color_scheme }} gradient slideshow__text--{{ block.settings.text_alignment }} slideshow__text-mobile--{{ block.settings.text_alignment_mobile }}">
                  {%- assign heading_content = block.settings.heading_liquid | default: block.settings.heading -%}
                  {%- if heading_content != blank -%}
                    <h2 class="banner__heading inline-richtext {{ block.settings.heading_size }}">
                      {{ heading_content }}
                    </h2>
                  {%- endif -%}
  
                  {%- assign subheading_content = block.settings.subheading_liquid | default: block.settings.subheading -%}
                  {%- if subheading_content != blank -%}
                    <div class="banner__text rte">
                      <p class="{{ block.settings.subheading_size }}">{{ subheading_content }}</p>
                    </div>
                  {%- endif -%}
  
                  {%- if block.settings.logo != blank -%}
                    <div class="banner__logo" {{ block.shopify_attributes }}>
                      {{ block.settings.logo | image_url: width: 250 | image_tag:
                        loading: 'lazy'
                      }}
                    </div>
                  {%- endif -%}
  
                  {%- assign button_label_content = block.settings.button_label_liquid | default: block.settings.button_label -%}
                  {%- if button_label_content != blank -%}
                    <div class="banner__buttons">
                      <a
                        {% if block.settings.link %}
                          href="{{ block.settings.link }}"
                        {% else %}
                          role="link" aria-disabled="true"
                        {% endif %}
                          class="{{ block.settings.button_style }}"
                        >
                          {{ button_label_content }}
                      </a>
                    </div>
                  {%- endif -%}
                </div>
              </div>
            </div>
          {%- endfor -%}
        </div>
  
        {%- if section.blocks.size > 1 and section.settings.auto_rotate == false -%}
          <div class="slideshow__controls slider-buttons{% if section.settings.show_text_below %} slideshow__controls--border-radius-mobile{% endif %}">
            <button
              type="button"
              class="slider-button slider-button--prev"
              name="previous"
              aria-label="{{ 'sections.slideshow.previous_slideshow' | t }}"
              aria-controls="Slider-{{ section.id }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
            <div class="slider-counter slider-counter--{{ section.settings.slider_visual }}{% if section.settings.slider_visual == 'counter' or section.settings.slider_visual == 'numbers' %} caption{% endif %}">
              {%- if section.settings.slider_visual == 'counter' -%}
                <span class="slider-counter--current">1</span>
                <span aria-hidden="true"> / </span>
                <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                <span class="slider-counter--total">{{ section.blocks.size }}</span>
              {%- else -%}
                <div class="slideshow__control-wrapper">
                  {%- for block in section.blocks -%}
                    <button
                      class="slider-counter__link slider-counter__link--{{ section.settings.slider_visual }} link"
                      aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                      aria-controls="Slider-{{ section.id }}"
                    >
                      {%- if section.settings.slider_visual == 'numbers' -%}
                        {{ forloop.index -}}
                      {%- else -%}
                        <span class="dot"></span>
                      {%- endif -%}
                    </button>
                  {%- endfor -%}
                </div>
              {%- endif -%}
            </div>
            <button
              type="button"
              class="slider-button slider-button--next"
              name="next"
              aria-label="{{ 'sections.slideshow.next_slideshow' | t }}"
              aria-controls="Slider-{{ section.id }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
  
            {%- if section.settings.auto_rotate -%}
              <button
                type="button"
                class="slideshow__autoplay slider-button{% if section.settings.auto_rotate == false %} slideshow__autoplay--paused{% endif %}"
                aria-label="{{ 'sections.slideshow.pause_slideshow' | t }}"
              >
                <span class="svg-wrapper">
                  {{- 'icon-pause.svg' | inline_asset_content -}}
                </span>
                <span class="svg-wrapper">
                  {{- 'icon-play.svg' | inline_asset_content -}}
                </span>
              </button>
            {%- endif -%}
          </div>
        {%- endif -%}
      </slideshow-component>
    </div>
  </div>
</div>

{%- if request.design_mode -%}
  <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.slideshow.name",
  "tag": "section",
  "class": "section section--slideshow",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Section color and layout"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.slideshow.blocks.slide.settings.color_scheme.info",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "section_width",
      "options": [
        {
          "value": "full-width",
          "label": "Full width on all devices"
        },
        {
          "value": "page-width",
          "label": "Page width (based on theme settings)"
        },
        {
          "value": "page-width page-width--narrow",
          "label": "Page width narrow (tablet and desktop only)"
        }
      ],
      "default": "full-width",
      "label": "Section width"
    },
    {
      "type": "select",
      "id": "slide_height_desktop",
      "options": [
        {
          "value": "small",
          "label": "Small - 42rem"
        },
        {
          "value": "medium",
          "label": "Medium - 56rem"
        },
        {
          "value": "large",
          "label": "Large - 72rem"
        },
        {
          "value": "adapt",
          "label": "Adapt to height of content"
        }
      ],
      "default": "medium",
      "label": "Desktop slide height",
      "info": "For devices with screens >750px" 
    },
    {
      "type": "select",
      "id": "slide_height_mobile",
      "options": [
        {
          "value": "small",
          "label": "Small - 42rem"
        },
        {
          "value": "medium",
          "label": "Medium - 56rem"
        },
        {
          "value": "large",
          "label": "Large - 72rem"
        },
        {
          "value": "adapt",
          "label": "Adapt to height of image"
        }
      ],
      "default": "medium",
      "label": "Mobile slide height",
      "info": "For devices with screens <749px"
    },
    {
      "type": "checkbox",
      "id": "show_text_below",
      "label": "t:sections.slideshow.settings.show_text_below.label",
      "default": true
    },
    {
      "type": "header",
      "content": "Slideshow options"
    },
    {
      "type": "select",
      "id": "slider_visual",
      "options": [
        {
          "value": "dots",
          "label": "t:sections.slideshow.settings.slider_visual.options__2.label"
        },
        {
          "value": "counter",
          "label": "t:sections.slideshow.settings.slider_visual.options__1.label"
        },
        {
          "value": "numbers",
          "label": "t:sections.slideshow.settings.slider_visual.options__3.label"
        }
      ],
      "default": "counter",
      "label": "t:sections.slideshow.settings.slider_visual.label"
    },
    {
      "type": "checkbox",
      "id": "auto_rotate",
      "label": "t:sections.slideshow.settings.auto_rotate.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "hide_pagination_borders",
      "label": "Hide pagination borders",
      "default": false
    },
    {
      "type": "range",
      "id": "change_slides_speed",
      "min": 3,
      "max": 9,
      "step": 2,
      "unit": "s",
      "label": "t:sections.slideshow.settings.change_slides_speed.label",
      "default": 5
    },
    {
      "type": "header",
      "content": "t:sections.all.animation.content"
    },
    {
      "type": "select",
      "id": "image_behavior",
      "options": [
        {
          "value": "none",
          "label": "t:sections.all.animation.image_behavior.options__1.label"
        },
        {
          "value": "ambient",
          "label": "t:sections.all.animation.image_behavior.options__2.label"
        }
      ],
      "default": "none",
      "label": "t:sections.all.animation.image_behavior.label"
    },
    {
      "type": "header",
      "content": "t:sections.slideshow.settings.accessibility.content"
    },
    {
      "type": "text",
      "id": "accessibility_info",
      "label": "t:sections.slideshow.settings.accessibility.label",
      "info": "t:sections.slideshow.settings.accessibility.info",
      "default": "t:sections.slideshow.settings.accessibility.default"
    },
    {
      "type": "header",
      "content": "Section spacing mobile (<750px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "page",
      "id": "page",
      "label": "Linked Page",
      "info": "Allows page linking to access metafields in unique cases (e.g. home page)."
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "t:sections.slideshow.blocks.slide.name",
      "limit": 5,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.slideshow.blocks.slide.settings.image.label"
        },
        {
          "type": "header",
          "content": "Content: heading"
        },
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.slideshow.blocks.slide.settings.heading.default",
          "label": "Heading"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h1",
          "label": "Heading size"
        },
        {
          "type": "header",
          "content": "Content: subheading"
        },
        {
          "type": "inline_richtext",
          "id": "subheading",
          "default": "t:sections.slideshow.blocks.slide.settings.subheading.default",
          "label": "t:sections.slideshow.blocks.slide.settings.subheading.label"
        },
        {
          "type": "liquid",
          "id": "subheading_liquid",
          "label": "Subheading liquid",
          "info": "Overrides Subheading above"
        },
        {
          "type": "select",
          "id": "subheading_size",
          "options": [
            {
              "value": "text-body",
              "label": "text-body"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "text-body",
          "label": "Subheading size"
        },
        {
          "type": "header",
          "content": "Optional: logo or small image"
        },
        {
          "type": "image_picker",
          "id": "logo",
          "label": "Logo or small image",
          "info": "Width is fixed at 250px"
        },
        {
          "type": "header",
          "content": "Buttons"
        },
        {
          "type": "inline_richtext",
          "id": "button_label",
          "default": "t:sections.slideshow.blocks.slide.settings.button_label.default",
          "label": "t:sections.slideshow.blocks.slide.settings.button_label.label",
          "info": "t:sections.slideshow.blocks.slide.settings.button_label.info"
        },
        {
          "type": "liquid",
          "id": "button_label_liquid",
          "label": "Button label liquid",
          "info": "Overrides Button label above"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.slideshow.blocks.slide.settings.link.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "Button style"
        },
        {
          "type": "header",
          "content": "Block color and layout"
        },
        {
          "type": "select",
          "id": "box_align",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__1.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__2.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__3.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__4.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__5.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__6.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__7.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__8.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__9.label"
            }
          ],
          "default": "middle-center",
          "label": "t:sections.slideshow.blocks.slide.settings.box_align.label",
          "info": "t:sections.slideshow.blocks.slide.settings.box_align.info"
        },
        {
          "type": "checkbox",
          "id": "show_text_box",
          "label": "t:sections.slideshow.blocks.slide.settings.show_text_box.label",
          "default": true
        },
        {
          "type": "select",
          "id": "text_alignment",
          "options": [
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_1.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_2.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_3.label"
            }
          ],
          "default": "center",
          "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.label"
        },
        {
          "type": "select",
          "id": "text_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__1.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__2.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__3.label"
            }
          ],
          "default": "center",
          "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.label"
        },
        {
          "type": "range",
          "id": "content_width_tablet",
          "min": 30,
          "max": 100,
          "step": 2,
          "unit": "rem",
          "label": "Content max width tablet (min-width: 750px) ",
          "default": 50
        },
        {
          "type": "range",
          "id": "content_width_desktop",
          "min": 50,
          "max": 150,
          "step": 2,
          "unit": "rem",
          "label": "Content max width desktop (min-width: 990px) ",
          "default": 72
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "t:sections.slideshow.blocks.slide.settings.image_overlay_opacity.label",
          "default": 0
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "label": "t:sections.all.colors.label",
          "info": "t:sections.slideshow.blocks.slide.settings.color_scheme.info",
          "default": "scheme-1"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.slideshow.presets.name",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}