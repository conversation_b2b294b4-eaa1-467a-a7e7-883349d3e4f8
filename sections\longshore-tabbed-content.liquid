{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

<style>
  .tabbed-content {
    position: relative;
  }
  tab-component {
   display: block;
  }
</style>

<div
  class="tabbed-content color-{{ section.settings.color_scheme_section }} gradient section-{{ section.id }}-padding {{ section.settings.parent_div_custom_css }} {% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
  {% if settings.animations_reveal_on_scroll %}
    data-cascade
  {% endif %}
>  
    <tab-component class="{% if section.settings.mobile_page_width %} page-width-mobile-only{% endif %}{% if section.settings.desktop_page_width %} page-width-desktop-only{% endif %} {{ section.settings.tab_component_custom_css }}">
      <div role="tablist">
        {% comment %}Tablist elements will be inserted dynamically by javascript{% endcomment %}
      </div>
      {% content_for 'blocks' %}
    </tab-component>
  </div>

{% schema %}
{
  "name": "Tabbed Content",
  "tag": "section",
  "class": "section section--tabbed-content",
  "settings": [
    {
      "type": "header",
      "content": "General Settings"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_section",
      "label": "Section color scheme",
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Mobile layout",
      "info": "Devices with screens < 750px"
    },
    {
      "type": "checkbox",
      "id": "mobile_page_width",
      "default": true,
      "label": "Use margins"
    },
    {
      "type": "number",
      "id": "padding_top_mobile",
      "label": "Padding top mobile"
    },
    {
      "type": "number",
      "id": "padding_bottom_mobile",
      "label": "Padding bottom mobile"
    },
    {
      "type": "header",
      "content": "Desktop layout",
      "info": "Devices with screens >= 750px"
    },
    {
      "type": "checkbox",
      "id": "desktop_page_width",
      "default": true,
      "label": "Use margins"
    },
    {
      "type": "number",
      "id": "padding_top_desktop",
      "label": "Padding top tablet/desktop"
    },
    {
      "type": "number",
      "id": "padding_bottom_desktop",
      "label": "Padding bottom tablet/desktop"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "text",
      "id": "parent_div_custom_css",
      "label": "Custom CSS classes for top <div>",
      "info": "The highest level component we can pass CSS classes to"
    },
    {
      "type": "text",
      "id": "tab_component_custom_css",
      "label": "Custom CSS classes for tab-component"
    },
    {
      "type": "text",
      "id": "tab_button_custom_css",
      "label": "Custom CSS classes for tab buttons",
      "default": "link"
    },
  ],
  "blocks": [{ "type": "_tab" }],
  "presets": [
    {
      "name": "Tabbed Content"
    }
  ]
}
{% endschema %}