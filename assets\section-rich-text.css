.rich-text {
  z-index: 1;
}

.rich-text__wrapper {
  display: flex;
  justify-content: center;
  /* bs-mod
    - deemed uneeded by new formatting options in section
  */
  width: 100%;
}

.rich-text__blocks {
  width: 100%;
}

@media screen and (min-width: 750px) {
  .rich-text__wrapper {
    width: 100%;
  }

  .rich-text__wrapper--left {
    justify-content: flex-start;
  }

  .rich-text__wrapper--right {
    justify-content: flex-end;
  }

  /* bs-del
    - removed as max-width is now controlled via section options
  */
}

.rich-text__blocks * {
  overflow-wrap: break-word;
}

.rich-text__blocks > * {
  margin-top: 0;
  margin-bottom: 0;
}

.rich-text__blocks > * + * {
  margin-top: 2rem;
}

.rich-text__blocks > * + a {
  margin-top: 3rem;
}

.rich-text__buttons {
  /* bs-del
    - removed as max-width, display, justify-content is deemed unecessary
  */
  flex-wrap: wrap;
  gap: 1rem;
  width: 100%;
  word-break: break-word;
}

.rich-text__buttons--multiple > * {
  /* bs-mod
    - removed as flex-grow is deemed unecessary
  */
  min-width: 22rem;
  margin-bottom: 1rem;
}

/* bs-add 
  - formatting now set here for buttons
*/
@media screen and (min-width: 750px) {
  .content--desktop-center .rich-text__buttons--multiple > * {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
  .content--desktop-left .rich-text__buttons--multiple > * {
    margin-right: 1rem;
  }
  .content--desktop-right .rich-text__buttons--multiple > * {
    margin-left: 1rem;
  }
}

@media screen and (max-width: 749px) {
  .content--mobile-center .rich-text__buttons--multiple > * {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
  .content--mobile-left .rich-text__buttons--multiple > * {
    margin-right: 1rem;
  }
  .content--mobile-right .rich-text__buttons--multiple > * {
    margin-left: 1rem;
  }
}

.rich-text__buttons + .rich-text__buttons {
  margin-top: 1rem;
}

.rich-text__blocks.left .rich-text__buttons {
  justify-content: flex-start;
}

.rich-text__blocks.right .rich-text__buttons {
  justify-content: flex-end;
}
