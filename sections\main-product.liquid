{% comment %}
  bs-add
  - padding options for accordion sides
  - liquid override for titles
  - text sizing options for headers
{% endcomment %}

<div class="{{ section.settings.custom_css_class }}">
  <product-info
    id="MainProduct-{{ section.id }}"
    class="section-{{ section.id }}-padding gradient color-{{ section.settings.color_scheme }}"
    data-section="{{ section.id }}"
    data-product-id="{{ product.id }}"
    data-update-url="true"
    data-url="{{ product.url }}"
    {% if section.settings.image_zoom == 'hover' %}
      data-zoom-on-hover
    {% endif %}
  >
    {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
    {{ 'component-accordion.css' | asset_url | stylesheet_tag }}
    {{ 'component-price.css' | asset_url | stylesheet_tag }}
    {{ 'component-slider.css' | asset_url | stylesheet_tag }}
    {{ 'component-rating.css' | asset_url | stylesheet_tag }}
    {{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}

    {% unless product.has_only_default_variant %}
      {{ 'component-product-variant-picker.css' | asset_url | stylesheet_tag }}
      {{ 'component-swatch-input.css' | asset_url | stylesheet_tag }}
      {{ 'component-swatch.css' | asset_url | stylesheet_tag }}
    {% endunless %}
    {%- if product.quantity_price_breaks_configured? -%}
      {{ 'component-volume-pricing.css' | asset_url | stylesheet_tag }}
    {%- endif -%}

    {%- style -%}
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top_mobile }}px;
        padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
      }

      .section-{{ section.id }}-padding .product__accordion .accordion__content {
        padding: 0 {{ section.settings.accordion_padding }}rem;
      }

      @media screen and (min-width: 750px) {
        .section-{{ section.id }}-padding {
          padding-top: {{ section.settings.padding_top }}px;
          padding-bottom: {{ section.settings.padding_bottom }}px;
        }
      }
    {%- endstyle -%}

    <script src="{{ 'product-info.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
    {%- if product.quantity_price_breaks_configured? -%}
      <script src="{{ 'show-more.js' | asset_url }}" defer="defer"></script>
      <script src="{{ 'price-per-item.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {% if section.settings.image_zoom == 'hover' %}
      <script id="EnableZoomOnHover-main" src="{{ 'magnify.js' | asset_url }}" defer="defer"></script>
    {% endif %}
    {%- if request.design_mode -%}
      <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}

    {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}
    {%- if first_3d_model -%}
      {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
      <link
        id="ModelViewerStyle"
        rel="stylesheet"
        href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css"
        media="print"
        onload="this.media='all'"
      >
      <link
        id="ModelViewerOverride"
        rel="stylesheet"
        href="{{ 'component-model-viewer-ui.css' | asset_url }}"
        media="print"
        onload="this.media='all'"
      >
    {%- endif -%}

    {% assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src' %}
    <div class="page-width">
      <div class="product product--{{ section.settings.media_size }} product--{{ section.settings.media_position }} product--{{ section.settings.gallery_layout }} product--mobile-{{ section.settings.mobile_thumbnails }} grid grid--1-col {% if product.media.size > 0 %}grid--2-col-tablet{% if section.settings.custom_liquid_column != blank %} grid--3-col-desktop{% endif %}{% else %}product--no-media{% endif %}">
        {%- if section.settings.custom_liquid_column != blank
          and section.settings.custom_column_placement == 'first'
        -%}
          <div class="grid__item product__custom-column-wrapper">
            {{ section.settings.custom_liquid_column }}
          </div>
        {%- endif %}
        <div class="grid__item product__media-wrapper{% if section.settings.custom_liquid_column != blank %} product__info-wrapper--custom-column{% endif %}">
          {% render 'product-media-gallery', variant_images: variant_images %}
        </div>
        <div class="product__info-wrapper grid__item{% if settings.page_width > 1400 and section.settings.media_size == "small" %} product__info-wrapper--extra-padding{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}{% if product.media.size > 0 and section.settings.custom_liquid_column != blank %} product__info-wrapper--custom-column{% endif %}">
          <section
            id="ProductInfo-{{ section.id }}"
            class="product__info-container{% if section.settings.enable_sticky_info %} product__column-sticky{% endif %} {{ section.settings.product__info_custom_css_class }}"
          >
            {%- assign product_form_id = 'product-form-' | append: section.id -%}

            {%- for block in section.blocks -%}
              {%- comment -%}
                bs-add
                - support for block show/hide
                - support for custom liquid before and after block content
                - support for custom classes at the block level
              {%- endcomment -%}
              <div
                id="MainProductBlock-{{ block.id }}"
                class="section-block section-block--{{ block.type | replace: '@', '' }} {{ block.settings.hide_size }} {{ block.settings.custom_css }}"
              >
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {% render block %}
                  {%- when 'text' -%}
                    {{ block.settings.liquid_before }}
                    <p
                      class="product__text inline-richtext{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {{- block.settings.text_liquid | default: block.settings.text -}}
                    </p>
                    {{ block.settings.liquid_after }}
                  {%- when 'title' -%}
                    {{ block.settings.liquid_before }}
                    <div class="product__title" {{ block.shopify_attributes }}>
                      <h1 class="{{ block.settings.text_style }}">{{ product.title | escape }}</h1>
                      <a href="{{ product.url }}" class="product__title">
                        <h2 class="h1">
                          {{ product.title | escape }}
                        </h2>
                      </a>
                    </div>
                    {{ block.settings.liquid_after }}
                  {%- when 'price' -%}
                    {{ block.settings.liquid_before }}
                    <div id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                      {%- render 'price',
                        product: product,
                        use_variant: true,
                        show_badges: true,
                        price_class: 'price--large'
                      -%}
                    </div>
                    {%- if product.quantity_price_breaks_configured? -%}
                      <div class="volume-pricing-note" id="Volume-Note-{{ section.id }}">
                        <span>{{ 'products.product.volume_pricing.note' | t }}</span>
                      </div>
                    {%- endif -%}

                    {%- if block.settings.custom_above_taxes_liquid != blank -%}
                      <div class="product__custom">
                        {{ block.settings.custom_above_taxes_liquid }}
                      </div>
                    {%- endif -%}

                    {%- if cart.taxes_included or cart.duties_included or shop.shipping_policy.body != blank -%}
                      <div class="product__tax caption rte">
                        {%- if cart.duties_included
                          and cart.taxes_included
                          and block.settings.show_duties_included
                          and block.settings.show_taxes_included
                        -%}
                          {{ 'products.product.duties_and_taxes_included' | t }}
                        {%- elsif cart.taxes_included and block.settings.show_taxes_included -%}
                          {{ 'products.product.taxes_included' | t }}
                        {%- elsif cart.duties_included and block.settings.show_duties_included -%}
                          {{ 'products.product.duties_included' | t }}
                        {%- endif -%}
                        {%- if shop.shipping_policy.body != blank and block.settings.show_shipping_policy -%}
                          {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                        {%- endif -%}
                      </div>
                    {%- endif -%}
                    <div {{ block.shopify_attributes }}>
                      {%- assign product_form_installment_id = 'product-form-installment-' | append: section.id -%}
                      {%- form 'product',
                        product,
                        id: product_form_installment_id,
                        class: 'installment caption-large'
                      -%}
                        <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                        {%- if block.settings.show_payment_terms -%}
                          {{ form | payment_terms }}
                        {%- endif -%}
                      {%- endform -%}
                    </div>
                    {{ block.settings.liquid_after }}
                  {%- when 'inventory' -%}
                    {{ block.settings.liquid_before }}
                    <p
                      class="product__inventory{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.inventory_management != 'shopify' %} visibility-hidden{% endif %}"
                      {{ block.shopify_attributes }}
                      id="Inventory-{{ section.id }}"
                      role="status"
                    >
                      {%- if product.selected_or_first_available_variant.inventory_management == 'shopify' -%}
                        {%- if product.selected_or_first_available_variant.inventory_quantity > 0 -%}
                          {%- if product.selected_or_first_available_variant.inventory_quantity
                              <= block.settings.inventory_threshold
                          -%}
                            <span class="svg-wrapper" style="color: rgb(238, 148, 65)">
                              {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                            </span>
                            {%- if block.settings.show_inventory_quantity -%}
                              {{-
                                'products.product.inventory_low_stock_show_count'
                                | t: quantity: product.selected_or_first_available_variant.inventory_quantity
                              -}}
                            {%- else -%}
                              {{- 'products.product.inventory_low_stock' | t -}}
                            {%- endif -%}
                          {%- else -%}
                            <span class="svg-wrapper" style="color: rgb(62, 214, 96)">
                              {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                            </span>
                            {%- if block.settings.show_inventory_quantity -%}
                              {{-
                                'products.product.inventory_in_stock_show_count'
                                | t: quantity: product.selected_or_first_available_variant.inventory_quantity
                              -}}
                            {%- else -%}
                              {{- 'products.product.inventory_in_stock' | t -}}
                            {%- endif -%}
                          {%- endif -%}
                        {%- else -%}
                          {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' -%}
                            <span class="svg-wrapper" style="color: rgb(62, 214, 96)">
                              {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                            </span>
                            {{- 'products.product.inventory_out_of_stock_continue_selling' | t -}}
                          {%- else -%}
                            <span class="svg-wrapper" style="color: rgb(200, 200, 200)">
                              {{- 'icon-inventory-status.svg' | inline_asset_content -}}
                            </span>
                            {{- 'products.product.inventory_out_of_stock' | t -}}
                          {%- endif -%}
                        {%- endif -%}
                      {%- endif -%}
                    </p>
                    {{ block.settings.liquid_after }}
                  {%- when 'description' -%}
                    {{ block.settings.liquid_before }}
                    {%- if product.description != blank -%}
                      <div class="product__description rte quick-add-hidden" {{ block.shopify_attributes }}>
                        {{ product.description }}
                      </div>
                    {%- endif -%}
                    {{ block.settings.liquid_after }}
                  {%- when 'sku' -%}
                    {{ block.settings.liquid_before }}
                    <p
                      class="product__sku{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}"
                      id="Sku-{{ section.id }}"
                      role="status"
                      {{ block.shopify_attributes }}
                    >
                      <span class="visually-hidden">{{ 'products.product.sku' | t }}:</span>
                      {{- product.selected_or_first_available_variant.sku -}}
                    </p>
                    {{ block.settings.liquid_after }}
                  {%- when 'custom_liquid' -%}
                    {{ block.settings.custom_liquid }}
                  {%- when 'collapsible_tab' -%}
                    {{ block.settings.liquid_before }}
                    {%- if block.settings.content_liquid != blank
                      or block.settings.content != blank
                      or block.settings.page.content != blank
                    %}
                      <div class="product__accordion accordion quick-add-hidden" {{ block.shopify_attributes }}>
                        <details
                          id="Details-{{ block.id }}-{{ section.id }}"
                          {% if block.settings.open_tab %}
                            open
                          {% endif %}
                        >
                          <summary>
                            <div class="summary__title">
                              {% render 'icon-accordion',
                                icon: block.settings.icon,
                                image_icon: block.settings.image_icon
                              %}
                              <h2 class="{{ section.settings.accordion_title_size }} accordion__title inline-richtext">
                                {% assign default_page_title = block.settings.page.title | escape %}
                                {{
                                  block.settings.heading_liquid
                                  | default: block.settings.heading
                                  | default: default_page_title
                                }}
                              </h2>
                            </div>
                            {{- 'icon-caret.svg' | inline_asset_content -}}
                          </summary>
                          <div class="accordion__content rte" id="ProductAccordion-{{ block.id }}-{{ section.id }}">
                            {{ block.settings.content_liquid | default: block.settings.content }}
                            {{ block.settings.page.content }}
                          </div>
                        </details>
                      </div>
                    {%- endif -%}
                    {{ block.settings.liquid_after }}
                  {%- when 'quantity_selector' -%}
                    {{ block.settings.liquid_before }}
                    <div
                      id="Quantity-Form-{{ section.id }}"
                      class="product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {% comment %} TODO: enable theme-check once `item_count_for_variant` is accepted as valid filter {% endcomment %}
                      {% # theme-check-disable %}
                      {%- assign cart_qty = cart
                        | item_count_for_variant: product.selected_or_first_available_variant.id
                      -%}
                      {% # theme-check-enable %}
                      <label class="quantity__label form__label" for="Quantity-{{ section.id }}">
                        {{ 'products.product.quantity.label' | t }}
                        <span class="quantity__rules-cart{% if cart_qty == 0 %} hidden{% endif %}">
                          {%- render 'loading-spinner' -%}
                          <span
                            >(
                            {{- 'products.product.quantity.in_cart_html' | t: quantity: cart_qty -}}
                            )</span
                          >
                        </span>
                      </label>
                      <div class="price-per-item__container">
                        <quantity-input class="quantity" data-url="{{ product.url }}" data-section="{{ section.id }}">
                          <button class="quantity__button" name="minus" type="button">
                            <span class="visually-hidden">
                              {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                            </span>
                            <span class="svg-wrapper">
                              {{- 'icon-minus.svg' | inline_asset_content -}}
                            </span>
                          </button>
                          <input
                            class="quantity__input"
                            type="number"
                            name="quantity"
                            id="Quantity-{{ section.id }}"
                            data-cart-quantity="{{ cart_qty }}"
                            data-min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                            min="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                            {% if product.selected_or_first_available_variant.quantity_rule.max != null %}
                              data-max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                              max="{{ product.selected_or_first_available_variant.quantity_rule.max }}"
                            {% endif %}
                            step="{{ product.selected_or_first_available_variant.quantity_rule.increment }}"
                            value="{{ product.selected_or_first_available_variant.quantity_rule.min }}"
                            form="{{ product_form_id }}"
                          >
                          <button class="quantity__button" name="plus" type="button">
                            <span class="visually-hidden">
                              {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                            </span>
                            <span class="svg-wrapper">
                              {{- 'icon-plus.svg' | inline_asset_content -}}
                            </span>
                          </button>
                        </quantity-input>
                        {%- liquid
                          assign volume_pricing_array = product.selected_or_first_available_variant.quantity_price_breaks | sort: 'quantity' | reverse
                          assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.min
                          if cart_qty > 0
                            assign current_qty_for_volume_pricing = cart_qty | plus: product.selected_or_first_available_variant.quantity_rule.increment
                          endif
                        -%}
                        {%- if product.quantity_price_breaks_configured? -%}
                          <price-per-item
                            id="Price-Per-Item-{{ section.id }}"
                            data-section-id="{{ section.id }}"
                            data-variant-id="{{ product.selected_or_first_available_variant.id }}"
                          >
                            {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                              {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                              <div class="price-per-item">
                                {%- if variant_price_compare -%}
                                  <dl class="price-per-item--current">
                                    <dt class="visually-hidden">
                                      {{ 'products.product.price.regular_price' | t }}
                                    </dt>
                                    <dd>
                                      <s class="variant-item__old-price">
                                        {{ variant_price_compare | money_with_currency }}
                                      </s>
                                    </dd>
                                  </dl>
                                {%- endif -%}
                                {%- if current_qty_for_volume_pricing < volume_pricing_array.last.minimum_quantity -%}
                                  {%- assign variant_price = product.selected_or_first_available_variant.price
                                    | money_with_currency
                                  -%}
                                  <span class="price-per-item--current">
                                    {{- 'products.product.volume_pricing.price_at_each' | t: price: variant_price -}}
                                  </span>
                                {%- else -%}
                                  {%- for price_break in volume_pricing_array -%}
                                    {%- if current_qty_for_volume_pricing >= price_break.minimum_quantity -%}
                                      {%- assign price_break_price = price_break.price | money_with_currency -%}
                                      <span class="price-per-item--current">
                                        {{-
                                          'products.product.volume_pricing.price_at_each'
                                          | t: price: price_break_price
                                        -}}
                                      </span>
                                      {%- break -%}
                                    {%- endif -%}
                                  {%- endfor -%}
                                {%- endif -%}
                              </div>
                            {%- else -%}
                              {%- assign variant_price = product.selected_or_first_available_variant.price
                                | money_with_currency
                              -%}
                              {%- assign variant_price_compare = product.selected_or_first_available_variant.compare_at_price -%}
                              <div class="price-per-item">
                                {%- if variant_price_compare -%}
                                  <dl class="price-per-item--current">
                                    <dt class="visually-hidden">
                                      {{ 'products.product.price.regular_price' | t }}
                                    </dt>
                                    <dd>
                                      <s class="variant-item__old-price">
                                        {{ variant_price_compare | money_with_currency }}
                                      </s>
                                    </dd>
                                    <dt class="visually-hidden">
                                      {{ 'products.product.price.sale_price' | t }}
                                    </dt>
                                    <dd>
                                      <span class="price-per-item--current">
                                        {{-
                                          'products.product.volume_pricing.price_at_each'
                                          | t: price: variant_price
                                        -}}
                                      </span>
                                    </dd>
                                  </dl>
                                {%- else -%}
                                  <span class="price-per-item--current">
                                    {{- 'products.product.volume_pricing.price_at_each' | t: price: variant_price -}}
                                  </span>
                                {%- endif -%}
                              </div>
                            {%- endif -%}
                          </price-per-item>
                        {%- endif -%}
                      </div>
                      <div class="quantity__rules caption" id="Quantity-Rules-{{ section.id }}">
                        {%- if product.selected_or_first_available_variant.quantity_rule.increment > 1 -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.multiples_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.increment
                            -}}
                          </span>
                        {%- endif -%}
                        {%- if product.selected_or_first_available_variant.quantity_rule.min > 1 -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.minimum_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.min
                            -}}
                          </span>
                        {%- endif -%}
                        {%- if product.selected_or_first_available_variant.quantity_rule.max != null -%}
                          <span class="divider">
                            {{-
                              'products.product.quantity.maximum_of'
                              | t: quantity: product.selected_or_first_available_variant.quantity_rule.max
                            -}}
                          </span>
                        {%- endif -%}
                      </div>
                      {%- if product.quantity_price_breaks_configured? -%}
                        <volume-pricing class="parent-display" id="Volume-{{ section.id }}">
                          {%- if product.selected_or_first_available_variant.quantity_price_breaks.size > 0 -%}
                            <span class="caption-large">{{ 'products.product.volume_pricing.title' | t }}</span>
                            <ul class="list-unstyled">
                              <li>
                                <span>{{ product.selected_or_first_available_variant.quantity_rule.min }}+</span>
                                {%- assign price = product.selected_or_first_available_variant.price
                                  | money_with_currency
                                -%}
                                <span data-text="{{ 'products.product.volume_pricing.price_at_each' | t: price: variant_price }}">
                                  {{- 'sections.quick_order_list.each' | t: money: price -}}
                                </span>
                              </li>
                              {%- for price_break in product.selected_or_first_available_variant.quantity_price_breaks -%}
                                {%- assign price_break_price = price_break.price | money_with_currency -%}
                                <li class="{%- if forloop.index >= 3 -%}show-more-item hidden{%- endif -%}">
                                  <span>
                                    {{- price_break.minimum_quantity -}}
                                    <span aria-hidden="true">+</span></span
                                  >
                                  <span data-text="{{ 'products.product.volume_pricing.price_at_each' | t: price: price_break_price }}">
                                    {{- 'sections.quick_order_list.each' | t: money: price_break_price -}}
                                  </span>
                                </li>
                              {%- endfor -%}
                            </ul>
                            {%- if product.selected_or_first_available_variant.quantity_price_breaks.size >= 3 -%}
                              <show-more-button>
                                <button
                                  class="button-show-more link underlined-link"
                                  id="Show-More-{{ section.id }}"
                                  type="button"
                                >
                                  <span class="label-show-more label-text"
                                    ><span aria-hidden="true">+ </span>{{ 'products.facets.show_more' | t }}
                                  </span>
                                </button>
                              </show-more-button>
                            {%- endif -%}
                          {%- endif -%}
                        </volume-pricing>
                      {%- endif -%}
                    </div>
                    {{ block.settings.liquid_after }}
                  {%- when 'popup' -%}
                    {{ block.settings.liquid_before }}
                    <modal-opener
                      class="product-popup-modal__opener quick-add-hidden"
                      data-modal="#PopupModal-{{ block.id }}"
                      {{ block.shopify_attributes }}
                    >
                      <button
                        id="ProductPopup-{{ block.id }}"
                        class="product-popup-modal__button link"
                        type="button"
                        aria-haspopup="dialog"
                      >
                        {% assign default_page_title = block.settings.page.title | escape %}
                        {{ block.settings.text_liquid | default: block.settings.text | default: default_page_title }}
                      </button>
                    </modal-opener>
                    {{ block.settings.liquid_after }}
                  {%- when 'share' -%}
                    {{ block.settings.liquid_before }}
                    {% liquid
                      assign share_url = product.selected_variant.url | default: product.url | prepend: request.origin
                      render 'share-button', block: block, share_link: share_url
                    %}
                    {{ block.settings.liquid_after }}
                  {%- when 'fake_variant_picker' -%}
                    {{ block.settings.liquid_before }}
                    {%- if product.metafields.custom.linked_products != blank -%}
                      <div class="fake-variants">
                        {%- if block.settings.fake_variants_name != blank -%}
                          {% assign show_color_swatches = block.settings.show_color_swatches %}
                          {% assign show_image_swatches = block.settings.show_image_swatches %}
                          {% if show_color_swatches or show_image_swatches %}
                            <p class="form__label">
                              <span>{{ block.settings.fake_variants_name }}:</span>
                              <span>
                                {% for fake_variant in product.metafields.custom.linked_products.value %}
                                  {% if fake_variant.handle == product.handle %}
                                    {% if fake_variant.metafields.custom.linked_products_name != blank %}
                                      {{ fake_variant.metafields.custom.linked_products_name }}
                                    {% else %}
                                      {{ fake_variant.title }}
                                    {% endif %}
                                  {% endif %}
                                {% endfor %}
                              </span>
                            </p>
                          {% else %}
                            <p class="form__label">{{ block.settings.fake_variants_name }}</p>
                          {% endif %}
                        {%- endif -%}
                        <ul class="{% if show_color_swatches or show_image_swatches %}fake-variants__swatches{% endif %}">
                          {%- for fake_variant in product.metafields.custom.linked_products.value -%}
                            {%- liquid
                              if show_color_swatches or show_image_swatches
                                assign variant_swatch_color = fake_variant.metafields.custom.linked_products_color_swatch
                              endif

                              assign variant_title = fake_variant.title
                              if fake_variant.metafields.custom.linked_products_name != blank
                                assign variant_title = fake_variant.metafields.custom.linked_products_name
                              endif

                              assign alt_badges = false
                              if show_color_swatches and variant_swatch_color != blank
                                assign alt_badges = true
                              elsif show_image_swatches
                                assign alt_badges = true
                              endif
                            -%}

                            <li
                              {% if alt_badges %}
                                class="product-color-swatch"
                                style="{% if variant_swatch_color != blank %}--color-swatch-background: {{ variant_swatch_color | default: 'transparent' }};{% endif %}{% if show_image_swatches == true %}--image-swatch-background: url({{ fake_variant.featured_image | image_url: width: 100 }});{% endif %}"
                                aria-label="{{ variant_title }}"
                              {% endif -%}
                              data-product-handle="{{ fake_variant.handle }}"
                            >
                              {%- if fake_variant.handle == product.handle -%}
                                <span class="fake-variants__label">
                                  {%- if alt_badges -%}
                                    <span class="product-color-swatch__inner"></span>
                                    <span class="visually-hidden">{{ variant_title }}</span>
                                  {%- else -%}
                                    {{ variant_title }}
                                  {%- endif -%}
                                </span>
                              {%- else -%}
                                {% comment %} Notes: prevent liquid warning until suport for more link filters {% endcomment %}
                                {% # theme-check-disable %}
                                <link rel="prefetch" href="{{ fake_variant.url }}" fetchpriority="auto" as="document">
                                {% # theme-check-enable %}

                                <a
                                  class="fake-variants__label"
                                  href="{{ fake_variant.url }}"
                                  {% if block.settings.enable_quick_fake_variant_change %}
                                    id="fakeVariantProductId-{{ fake_variant.id }}"
                                    data-product-id="{{ fake_variant.id }}"
                                    data-update-url="true"
                                    data-product-url="{{ fake_variant.url }}"
                                    data-url="{{ fake_variant.url }}"
                                  {% endif %}
                                >
                                  {%- if alt_badges -%}
                                    <span class="product-color-swatch__inner"></span>
                                    <span class="visually-hidden">{{ variant_title }}</span>
                                  {%- else -%}
                                    {{ variant_title }}
                                  {%- endif -%}
                                </a>
                              {%- endif -%}
                            </li>
                          {%- endfor -%}
                        </ul>
                      </div>
                    {%- endif -%}
                    {{ block.settings.liquid_after }}
                  {%- when 'variant_picker' -%}
                    {{ block.settings.liquid_before }}
                    {%- assign product_variants_size = product.variants | size -%}
                    {%- unless block.settings.hide_single_product_variant_swatches and product_variants_size < 2 -%}
                      {% render 'product-variant-picker',
                        product: product,
                        block: block,
                        product_form_id: product_form_id
                      %}
                    {%- endunless -%}
                    {{ block.settings.liquid_after }}
                  {%- when 'buy_buttons' -%}
                    {{ block.settings.liquid_before }}
                    {%- render 'buy-buttons',
                      block: block,
                      product: product,
                      product_form_id: product_form_id,
                      section_id: section.id,
                      show_pickup_availability: true
                    -%}
                    {{ block.settings.liquid_after }}
                  {%- when 'rating' -%}
                    {{ block.settings.liquid_before }}
                    {%- if product.metafields.reviews.rating.value != blank -%}
                      <div class="rating-wrapper">
                        {% liquid
                          assign rating_decimal = 0
                          assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                          if decimal >= 0.3 and decimal <= 0.7
                            assign rating_decimal = 0.5
                          elsif decimal > 0.7
                            assign rating_decimal = 1
                          endif
                        %}
                        <div
                          class="rating"
                          role="img"
                          aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                        >
                          <span
                            aria-hidden="true"
                            class="rating-star"
                            style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
                          ></span>
                        </div>
                        <p class="rating-text caption">
                          <span aria-hidden="true">
                            {{- product.metafields.reviews.rating.value }} /
                            {{ product.metafields.reviews.rating.value.scale_max -}}
                          </span>
                        </p>
                        <p class="rating-count caption">
                          {%- if block.settings.rating_display != blank -%}
                            <span aria-hidden="true">
                              {{- product.metafields.reviews.rating.value.rating | round: 2 -}}
                            </span>
                          {%- else -%}
                            <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                          {%- endif -%}
                          <span class="visually-hidden">
                            {{- product.metafields.reviews.rating_count }}
                            {{ 'accessibility.total_reviews' | t -}}
                          </span>
                        </p>
                      </div>
                    {%- endif -%}
                    {{ block.settings.liquid_after }}
                  {%- when 'complementary' -%}
                    {{ block.settings.liquid_before }}
                    {%- liquid
                      assign override_recommendations = false
                      assign recommendations_product_list = recommendations.products
                      assign recommendations_product_count = recommendations.products_count

                      if block.settings.recommendation_product_list != blank
                        assign override_recommendations = true
                        assign recommendations_product_list = block.settings.recommendation_product_list
                        assign recommendations_product_count = block.settings.recommendation_product_list.count
                      elsif block.settings.recommendation_collection != blank
                        assign override_recommendations = true
                        assign recommendations_product_list = block.settings.recommendation_collection.products
                        assign recommendations_product_count = block.settings.recommendation_collection.products_count
                        if recommendations_product_count > block.settings.product_list_limit
                          assign recommendations_product_count = block.settings.product_list_limit
                        endif
                      endif
                    -%}

                    {% liquid
                      assign media_aspect_ratio = block.settings.image_ratio | default: settings.card_image_ratio
                      assign quick_add_behavior = section.settings.quick_add_behavior | default: settings.card_quick_add_behavior
                      assign card_heading_font_size = block.settings.card_heading_font_size | default: settings.card_heading_font_size
                      assign show_alternative_title = settings.card_show_alternative_title
                      if section.settings.show_alternative_title == 'true'
                        assign show_alternative_title = true
                      elsif section.settings.show_alternative_title == 'false'
                        assign show_alternative_title = false
                      endif
                      assign show_card_product_custom_field = settings.card_show_card_product_custom_field
                      if section.settings.show_card_product_custom_field == 'true'
                        assign show_card_product_custom_field = true
                      elsif section.settings.show_card_product_custom_field == 'false'
                        assign show_card_product_custom_field = false
                      endif
                    %}

                    <product-recommendations
                      class="complementary-products quick-add-hidden{% if block.settings.make_collapsible_row %} is-accordion{% endif %}{% if block.settings.enable_quick_add %} complementary-products-contains-quick-add{% endif %}"
                      data-url="{{ routes.product_recommendations_url }}?limit={{ block.settings.product_list_limit }}&intent={{ block.settings.recommendation_type }}"
                      data-section-id="{{ section.id }}"
                      data-product-id="{{ product.id }}"
                      data-override="{{ override_recommendations | json }}"
                    >
                      {%- if recommendations_product_count > 0
                        and override_recommendations
                        or recommendations.performed
                      -%}
                        <aside
                          aria-label="{{ 'accessibility.complementary_products' | t }}"
                          {{ block.shopify_attributes -}}
                          {% if block.settings.make_collapsible_row %}
                            class="product__accordion accordion"
                          {% endif %}
                        >
                          <div class="complementary-products__container {% if block.settings.make_collapsible_row == false and block.settings.color_scheme != settings.theme_color_scheme %}complementary-products__container--padding content-container color-{{ block.settings.color_scheme }} gradient{% endif %}">
                            {%- if block.settings.make_collapsible_row -%}
                              <details id="Details-{{ block.id }}-{{ section.id }}" open>
                                <summary>
                            {%- endif %}
                            <div class="summary__title rte">
                              {%- if block.settings.make_collapsible_row -%}
                                {% render 'icon-accordion',
                                  icon: block.settings.icon,
                                  image_icon: block.settings.image_icon
                                %}
                                <h2 class="accordion__title {{ section.settings.accordion_title_size }}">
                                  {{ block.settings.block_heading_liquid | default: block.settings.block_heading }}
                                </h2>
                              {%- else -%}
                                <h2 class="{{ block.settings.block_heading_title_size }} accordion__title">
                                  {{ block.settings.block_heading_liquid | default: block.settings.block_heading }}
                                </h2>
                              {%- endif -%}
                              {%- if block.settings.make_collapsible_row == false
                                and block.settings.block_description != blank
                              -%}
                                <div class="summary__title__description">
                                  {{ block.settings.block_description }}
                                </div>
                              {%- endif -%}
                            </div>
                            {%- if block.settings.make_collapsible_row -%}
                              {{- 'icon-caret.svg' | inline_asset_content -}}
                              </summary>
                            {%- endif -%}
                            <slideshow-component class="slider-mobile-gutter">
                              {%- assign number_of_slides = recommendations_product_count
                                | plus: 0.0
                                | divided_by: block.settings.products_per_page
                                | ceil
                              -%}
                              {%- liquid
                                assign is_slider_style_horizontal = false
                                if block.settings.slider_style == 'horizontal'
                                  assign is_slider_style_horizontal = true
                                endif
                              -%}
                              <div
                                id="Slider-{{ block.id }}"
                                class="contains-card contains-card--product complementary-slider complementary-slider--{{ block.settings.slider_style }} grid grid--1-col slider slider--everywhere"
                                role="list"
                                {% if number_of_slides > 1 %}
                                  aria-label="{{ 'general.slider.name' | t }}"
                                {% endif %}
                              >
                                {% assign skip_card_product_styles = false %}
                                {%- for i in (1..number_of_slides) -%}
                                  <div
                                    id="Slide-{{ block.id }}-{{ forloop.index }}"
                                    class="complementary-slide complementary-slide--{{ settings.card_style }} grid__item slider__slide slideshow__slide"
                                    tabindex="-1"
                                    role="group"
                                    {% if number_of_slides > 1 %}
                                      aria-roledescription="{{ 'sections.slideshow.slide' | t }}"
                                      aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                                    {% endif %}
                                  >
                                    <ul
                                      class="list-unstyled {% if is_slider_style_horizontal %}grid grid--{{ block.settings.products_per_page }}-col grid--{{ block.settings.products_per_page }}-col-desktop{% endif %}"
                                      role="list"
                                    >
                                      {%- for product in recommendations_product_list
                                        limit: block.settings.products_per_page
                                        offset: continue
                                      -%}
                                        <li class="{% if is_slider_style_horizontal %}grid__item{% endif %}">
                                          {%- if block.settings.slider_style == 'horizontal' -%}
                                            {% render 'card-product',
                                              card_product: product,
                                              media_aspect_ratio: media_aspect_ratio,
                                              show_secondary_image: false,
                                              lazy_load: false,
                                              skip_styles: skip_card_product_styles,
                                              quick_add: block.settings.quick_add,
                                              quick_add_behavior: quick_add_behavior,
                                              card_heading_font_size: card_heading_font_size,
                                              section_id: section.id,
                                              quick_add_button_style: block.settings.quick_add_button_style,
                                              show_alternative_title: show_alternative_title,
                                              show_card_product_custom_field: show_card_product_custom_field
                                            %}
                                          {%- else -%}
                                            {% render 'card-product',
                                              card_product: product,
                                              media_aspect_ratio: media_aspect_ratio,
                                              show_secondary_image: false,
                                              lazy_load: false,
                                              skip_styles: skip_card_product_styles,
                                              quick_add: block.settings.quick_add,
                                              quick_add_behavior: quick_add_behavior,
                                              card_heading_font_size: card_heading_font_size,
                                              section_id: section.id,
                                              quick_add_button_style: block.settings.quick_add_button_style,
                                              show_alternative_title: show_alternative_title,
                                              show_card_product_custom_field: show_card_product_custom_field,
                                              horizontal_class: true,
                                              horizontal_quick_add: true
                                            %}
                                          {%- endif -%}
                                        </li>
                                        {%- assign skip_card_product_styles = true -%}
                                      {%- endfor -%}
                                    </ul>
                                  </div>
                                {%- endfor -%}
                              </div>
                              {%- if number_of_slides > 1 -%}
                                <div class="slider-buttons">
                                  <button
                                    type="button"
                                    class="slider-button slider-button--prev"
                                    name="previous"
                                    aria-label="{{ 'general.slider.previous_slide' | t }}"
                                  >
                                    <span class="svg-wrapper">
                                      {{- 'icon-caret.svg' | inline_asset_content -}}
                                    </span>
                                  </button>
                                  <div class="slider-counter slider-counter--{{ block.settings.pagination_style }}{% if block.settings.pagination_style == 'counter' or block.settings.pagination_style == 'numbers' %} caption{% endif %}">
                                    {%- if block.settings.pagination_style == 'counter' -%}
                                      <span class="slider-counter--current">1</span>
                                      <span aria-hidden="true"> / </span>
                                      <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                                      <span class="slider-counter--total">{{ number_of_slides }}</span>
                                    {%- else -%}
                                      <div class="slideshow__control-wrapper">
                                        {%- for i in (1..number_of_slides) -%}
                                          <button
                                            class="slider-counter__link slider-counter__link--{{ block.settings.pagination_style }} link"
                                            aria-label="{{ 'sections.slideshow.load_slide' | t }} {{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                                            aria-controls="Slider-{{ block.id }}"
                                          >
                                            {%- if block.settings.pagination_style == 'numbers' -%}
                                              {{ forloop.index -}}
                                            {%- else -%}
                                              <span class="dot"></span>
                                            {%- endif -%}
                                          </button>
                                        {%- endfor -%}
                                      </div>
                                    {%- endif -%}
                                  </div>
                                  <button
                                    type="button"
                                    class="slider-button slider-button--next"
                                    name="next"
                                    aria-label="{{ 'general.slider.next_slide' | t }}"
                                  >
                                    <span class="svg-wrapper">
                                      {{- 'icon-caret.svg' | inline_asset_content -}}
                                    </span>
                                  </button>
                                </div>
                              {%- endif -%}
                            </slideshow-component>
                            {%- if block.settings.make_collapsible_row -%}
                              </details>
                            {%- endif -%}
                          </div>
                        </aside>
                      {%- endif -%}
                      {{ 'component-card.css' | asset_url | stylesheet_tag }}
                      {{ 'component-complementary-products.css' | asset_url | stylesheet_tag }}
                      {%- unless block.settings.quick_add == 'none' -%}
                        {{ 'quick-add.css' | asset_url | stylesheet_tag }}
                        <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
                      {%- endunless -%}
                    </product-recommendations>
                    {{ block.settings.liquid_after }}
                  {%- when 'icon-with-text' -%}
                    {{ block.settings.liquid_before }}
                    {% render 'icon-with-text', block: block %}
                    {{ block.settings.liquid_after }}
                {%- endcase -%}
              </div>
            {%- endfor -%}
            <a href="{{ product.url }}" class="link product__view-details animate-arrow">
              {{ 'products.product.view_full_details' | t }}
              {{- 'icon-arrow.svg' | inline_asset_content -}}
            </a>
          </section>
        </div>
        {%- if section.settings.custom_liquid_column != blank and section.settings.custom_column_placement == 'last' -%}
          <div class="grid__item product__custom-column-wrapper">
            {{ section.settings.custom_liquid_column }}
          </div>
        {%- endif %}
      </div>

      {%- assign buy_bar_blocks = section.blocks | where: 'type', 'buy_bar' -%}
      {%- for block in buy_bar_blocks -%}
        <div class="{{ block.settings.hide_size }}" {{ block.shopify_attributes }}>
          <div class="product__buy_buttons--fixed hidden gradient color-{{ block.settings.color_scheme_buy_bar }}">
            <div class="page-width">
              <div class="product product--{{ section.settings.media_size }} product--{{ section.settings.media_position }} product--{{ section.settings.gallery_layout }} product--mobile-{{ section.settings.mobile_thumbnails }} grid grid--1-col {% if product.media.size > 0 %}grid--2-col-tablet{% if section.settings.custom_liquid_column != blank %} grid--3-col-desktop{% endif %}{% else %}product--no-media{% endif %}">
                {%- if section.settings.custom_liquid_column != blank
                  and section.settings.custom_column_placement == 'first'
                -%}
                  <div class="grid__item product__custom-column-wrapper">
                    <span></span>
                  </div>
                {%- endif %}
                <div class="grid__item product__media-wrapper{% if section.settings.custom_liquid_column != blank %} product__info-wrapper--custom-column{% endif %}">
                  <span></span>
                </div>
                <div class="product__info-wrapper grid__item{% if settings.page_width > 1400 and section.settings.media_size == "small" %} product__info-wrapper--extra-padding{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}{% if product.media.size > 0 and section.settings.custom_liquid_column != blank %} product__info-wrapper--custom-column{% endif %}">
                  <div class="product-form__buttons">
                    <button
                      id="ProductSubmitButton-{{ section.id }}-1"
                      type="submit"
                      name="add"
                      class="product-form__submit button--full-width {{ block.settings.button_style }}"
                      {% if product.selected_or_first_available_variant.available == false %}
                        disabled
                      {% endif %}
                      form="{{ product_form_id }}"
                      data-form-id="{{ product_form_id }}"
                    >
                      <span>
                        {%- if product.selected_or_first_available_variant.available == false -%}
                          {{ 'products.product.sold_out' | t }}
                        {%- else -%}
                          {{ 'products.product.add_to_cart' | t }}
                        {%- endif -%}
                      </span>
                      {%- render 'loading-spinner' -%}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <script>
          document.addEventListener("DOMContentLoaded", function() {
            showHideBuyBar();
          });

          function showHideBuyBar() {
            const mainFormBuyButton = document.querySelector('#shopify-section-{{ section.id }} .section-block--buy_buttons');
            const buyButtonBar = document.querySelector('#shopify-section-{{ section.id }} .product__buy_buttons--fixed');
            if (mainFormBuyButton && buyButtonBar) {
              const mainFormBuyButtonObserver = new IntersectionObserver(function(entries) {
                buyButtonBar.classList.toggle('hidden', entries[0].isIntersecting);
              }, {
                root: null,
                rootMargin: '0px',
                threshold: 0.5
              });
              window.addEventListener('scroll', mainFormBuyButtonObserver.observe(mainFormBuyButton), false);
            }
          }

          {% if request.design_mode %}
            document.addEventListener("shopify:section:load", showHideBuyBar);
          {% endif %}
        </script>
      {%- endfor -%}

      {% render 'product-media-modal', variant_images: variant_images %}

      {% assign popups = section.blocks | where: 'type', 'popup' %}
      {%- for block in popups -%}
        <modal-dialog
          id="PopupModal-{{ block.id }}"
          class="product-popup-modal color-{{ block.settings.color_scheme }} gradient"
          {{ block.shopify_attributes }}
        >
          <div
            role="dialog"
            aria-label="{{ block.settings.text_liquid | default: block.settings.text | strip_html | strip }}"
            aria-modal="true"
            class="product-popup-modal__content"
            tabindex="-1"
          >
            <button
              id="ModalClose-{{ block.id }}"
              type="button"
              class="product-popup-modal__toggle"
              aria-label="{{ 'accessibility.close' | t }}"
            >
              {{- 'icon-close.svg' | inline_asset_content -}}
            </button>
            <div class="product-popup-modal__content-info">
              {% assign default_product_title = block.settings.page.title | escape %}
              <h3 class="h2">{{ block.settings.title | default: default_product_title }}</h3>
              {{ block.settings.content_liquid | default: block.settings.page.content }}
            </div>
          </div>
        </modal-dialog>
      {%- endfor -%}

      {%- if product.media.size > 0 -%}
        <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
        <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
      {%- endif -%}

      {%- if first_3d_model -%}
        <script type="application/json" id="ProductJSON-{{ product.id }}">
          {{ product.media | where: 'media_type', 'model' | json }}
        </script>
        <script src="{{ 'product-model.js' | asset_url }}" defer></script>
      {%- endif -%}

      {%- liquid
        if product.selected_or_first_available_variant.featured_media
          assign seo_media = product.selected_or_first_available_variant.featured_media
        else
          assign seo_media = product.featured_media
        endif
      -%}

      <script type="application/ld+json">
        {{ product | structured_data }}
      </script>
    </div>
  </product-info>
</div>

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.main-product.name",
  "tag": "section",
  "class": "section section--main-product",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "t:sections.main-product.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "t:sections.main-product.blocks.text.settings.text.default",
          "label": "Text"
        },
        {
          "type": "liquid",
          "id": "text_liquid",
          "label": "Text liquid",
          "info": "Overrides Text above"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.text.settings.text_style.label"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-product.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "price",
      "name": "t:sections.main-product.blocks.price.name",
      "limit": 1,
      "settings": [
        {
          "type": "liquid",
          "id": "custom_above_taxes_liquid",
          "label": "Custom liquid",
          "info": "Placed above taxes"
        },
        {
          "type": "checkbox",
          "id": "show_taxes_included",
          "label": "Show included taxes",
          "info": "Copy is located under 'Include taxes' in Theme content",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_duties_included",
          "label": "Show duties taxes",
          "info": "Copy is located under 'Include duties' in Theme content",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_shipping_policy",
          "label": "Show shipping policy",
          "info": "Copy is located under 'Shipping policy html' in Theme content",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_payment_terms",
          "label": "Show payment terms",
          "default": true
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "sku",
      "name": "t:sections.main-product.blocks.sku.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            }
          ],
          "default": "h2",
          "label": "Text size"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.sku.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.sku.settings.text_style.label"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "inventory",
      "name": "t:sections.main-product.blocks.inventory.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.inventory.settings.text_style.label"
        },
        {
          "type": "range",
          "id": "inventory_threshold",
          "label": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.label",
          "min": 0,
          "max": 100,
          "step": 1,
          "info": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.info",
          "default": 10
        },
        {
          "type": "checkbox",
          "id": "show_inventory_quantity",
          "label": "t:sections.main-product.blocks.inventory.settings.show_inventory_quantity.label",
          "default": true
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "t:sections.main-product.blocks.quantity_selector.name",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "fake_variant_picker",
      "name": "Fake variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Fake variants allow you to link multiple products together to simulate regular product variants. To configure fake variants you must create the following product metafields:"
        },
        {
          "type": "paragraph",
          "content": "custom.linked_products (type product list)"
        },
        {
          "type": "paragraph",
          "content": "custom.linked_products_name (type: single line text)"
        },
        {
          "type": "paragraph",
          "content": "Please note: When assigning products to the fake variant metafield, you must include the product itself. You also need to make sure that you assign the products in the same order for every product in the group to create a consistent user experience."
        },
        {
          "type": "text",
          "id": "fake_variants_name",
          "default": "Fake variant name",
          "label": "Variant Name"
        },
        {
          "type": "checkbox",
          "id": "show_color_swatches",
          "label": "Show color swatches",
          "info": "Requires a product metafield (type: color picker) with namespace and key set to: custom.linked_products_color_swatch.",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_image_swatches",
          "label": "Show image swatches",
          "default": false
        },
        {
          "type": "header",
          "content": "Advanced Loading Features"
        },
        {
          "type": "checkbox",
          "id": "enable_quick_fake_variant_change",
          "label": "Enable quick fake variant change",
          "default": false
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "variant_picker",
      "name": "t:sections.main-product.blocks.variant_picker.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "picker_type",
          "options": [
            {
              "value": "dropdown",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__1.label"
            },
            {
              "value": "button",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__2.label"
            }
          ],
          "default": "button",
          "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.label"
        },
        {
          "id": "swatch_shape",
          "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.label",
          "type": "select",
          "info": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.info",
          "options": [
            {
              "value": "circle",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__1.label"
            },
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__2.label"
            },
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.variant_picker.settings.swatch_shape.options__3.label"
            }
          ],
          "default": "circle"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "checkbox",
          "id": "hide_single_product_variant_swatches",
          "default": false,
          "label": "Hide product swatches with single variant"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.main-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": false,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        },
        {
          "type": "liquid",
          "id": "custom_liquid_buttons",
          "label": "Buttons liquid",
          "info": "Shows after dynamic checkout buttons"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "Button style"
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "default": false,
          "label": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
          "info": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "description",
      "name": "t:sections.main-product.blocks.description.name",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "share",
      "name": "t:sections.main-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "inline_richtext",
          "id": "share_label",
          "label": "Text",
          "default": "t:sections.main-product.blocks.share.settings.text.default"
        },
        {
          "type": "liquid",
          "id": "share_label_liquid",
          "label": "Text liquid",
          "info": "Overrides Text above"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.share.settings.title_info.content"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "collapsible_tab",
      "name": "t:sections.main-product.blocks.collapsible_tab.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.main-product.blocks.collapsible_tab.settings.heading.default",
          "label": "Heading"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "none",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "image_picker",
          "id": "image_icon",
          "label": "Image icon",
          "info": "Overrides icon above"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
        },
        {
          "type": "liquid",
          "id": "content_liquid",
          "label": "Row content liquid",
          "info": "Overrides content above"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
        },
        {
          "type": "checkbox",
          "id": "open_tab",
          "default": false,
          "label": "Open tab on page load"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.main-product.blocks.popup.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "t:sections.main-product.blocks.popup.settings.link_label.default",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "type": "liquid",
          "id": "text_liquid",
          "label": "Link label liquid",
          "info": "Overrides Link label above"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "Title",
          "info": "Overrides page title if set"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        },
        {
          "type": "liquid",
          "id": "content_liquid",
          "label": "Custom content",
          "info": "Overrides page setting above"
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme-1",
          "label": "t:sections.all.colors.label"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.main-product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.rating.settings.paragraph.content"
        },
        {
          "type": "select",
          "id": "rating_display",
          "options": [
            {
              "value": "",
              "label": "Total reviews"
            },
            {
              "value": "average",
              "label": "Average rating"
            }
          ],
          "default": "",
          "label": "Rating display"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "complementary",
      "name": "t:sections.main-product.blocks.complementary_products.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.complementary_products.settings.paragraph.content"
        },
        {
          "type": "inline_richtext",
          "id": "block_heading",
          "default": "t:sections.main-product.blocks.complementary_products.settings.heading.default",
          "label": "Heading"
        },
        {
          "type": "liquid",
          "id": "block_heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "select",
          "id": "block_heading_title_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            }
          ],
          "default": "h3",
          "label": "Heading title size",
          "info": "Does not apply when in accordion mode."
        },
        {
          "type": "richtext",
          "id": "block_description",
          "label": "Description",
          "info": "Does not apply when in accordion mode."
        },
        {
          "type": "checkbox",
          "id": "make_collapsible_row",
          "default": false,
          "label": "t:sections.main-product.blocks.complementary_products.settings.make_collapsible_row.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "price_tag",
          "info": "t:sections.main-product.blocks.complementary_products.settings.icon.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "image_picker",
          "id": "image_icon",
          "label": "Image icon",
          "info": "Overrides icon above"
        },
        {
          "type": "range",
          "id": "product_list_limit",
          "min": 1,
          "max": 10,
          "step": 1,
          "default": 10,
          "label": "t:sections.main-product.blocks.complementary_products.settings.product_list_limit.label"
        },
        {
          "type": "range",
          "id": "products_per_page",
          "min": 1,
          "max": 3,
          "step": 1,
          "default": 2,
          "label": "t:sections.main-product.blocks.complementary_products.settings.products_per_page.label"
        },
        {
          "type": "select",
          "id": "pagination_style",
          "options": [
            {
              "value": "dots",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_1"
            },
            {
              "value": "counter",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_2"
            },
            {
              "value": "numbers",
              "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.options.option_3"
            }
          ],
          "label": "t:sections.main-product.blocks.complementary_products.settings.pagination_style.label",
          "default": "counter"
        },
        {
          "type": "select",
          "id": "slider_style",
          "options": [
            {
              "value": "horizontal",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__1.label"
            },
            {
              "value": "vertical",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__2.label"
            }
          ],
          "label": "Card direction",
          "default": "horizontal"
        },
        {
          "type": "select",
          "id": "quick_add",
          "default": "none",
          "label": "t:sections.main-collection-product-grid.settings.quick_add.label",
          "info": "t:sections.main-collection-product-grid.settings.quick_add.info",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_1"
            },
            {
              "value": "standard",
              "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_2"
            },
            {
              "value": "bulk",
              "label": "t:sections.main-collection-product-grid.settings.quick_add.options.option_3"
            }
          ]
        },
        {
          "type": "select",
          "id": "quick_add_button_style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "Quick add button style"
        },
        {
          "type": "header",
          "content": "Optional: Product card overrides"
        },
        {
          "type": "paragraph",
          "content": "Product cards are configured in theme settings. To override values for this section only, use the inputs below."
        },
        {
          "type": "text",
          "id": "image_ratio",
          "label": "t:sections.featured-collection.settings.image_ratio.label",
          "info": "Options: square, portrait, adapt"
        },
        {
          "type": "text",
          "id": "card_heading_font_size",
          "label": "Card heading font size",
          "info": "Options: h1, h2, h3, h4, h5, h6 etc."
        },
        {
          "type": "text",
          "id": "quick_add_behavior",
          "label": "Quick add behavior"
        },
        {
          "type": "text",
          "id": "show_alternative_title",
          "label": "Show alternative title",
          "info": "Options: true, false"
        },
        {
          "type": "text",
          "id": "show_card_product_custom_field",
          "label": "Show custom field",
          "info": "Options: true, false"
        },
        {
          "type": "header",
          "content": "Color Scheme"
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme-1",
          "label": "t:sections.all.colors.label"
        },
        {
          "type": "header",
          "content": "Advanced Recommendation Options"
        },
        {
          "type": "select",
          "id": "recommendation_type",
          "options": [
            {
              "value": "related",
              "label": "Related"
            },
            {
              "value": "complementary",
              "label": "Complementary"
            }
          ],
          "default": "complementary",
          "label": "Recommendation type"
        },
        {
          "type": "product_list",
          "id": "recommendation_product_list",
          "label": "Products",
          "limit": 10,
          "info": "Overrides Search & Discovery app"
        },
        {
          "type": "collection",
          "id": "recommendation_collection",
          "label": "Collection",
          "info": "Overrides Search & Discovery app"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "icon-with-text",
      "name": "t:sections.main-product.blocks.icon_with_text.name",
      "settings": [
        {
          "type": "select",
          "id": "layout",
          "options": [
            {
              "value": "horizontal",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__1.label"
            },
            {
              "value": "vertical",
              "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.options__2.label"
            }
          ],
          "default": "horizontal",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.layout.label"
        },
        {
          "type": "header",
          "content": "t:sections.main-product.blocks.icon_with_text.settings.content.label",
          "info": "t:sections.main-product.blocks.icon_with_text.settings.content.info"
        },
        {
          "type": "select",
          "id": "icon_1",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "heart",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_1.label"
        },
        {
          "type": "image_picker",
          "id": "image_1",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_1.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_1",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_1.default",
          "label": "First heading",
          "info": "Leave the heading label blank to hide the icon column."
        },
        {
          "type": "liquid",
          "id": "heading_1_liquid",
          "label": "First heading liquid",
          "info": "Overrides First heading above"
        },
        {
          "type": "select",
          "id": "icon_2",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "return",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_2.label"
        },
        {
          "type": "image_picker",
          "id": "image_2",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_2.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_2",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_2.default",
          "label": "Second heading",
          "info": "Leave the heading label blank to hide the icon column."
        },
        {
          "type": "liquid",
          "id": "heading_2_liquid",
          "label": "Second heading liquid",
          "info": "Overrides Second heading above"
        },
        {
          "type": "select",
          "id": "icon_3",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "not_allowed",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23_1.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "truck",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.icon_3.label"
        },
        {
          "type": "image_picker",
          "id": "image_3",
          "label": "t:sections.main-product.blocks.icon_with_text.settings.image_3.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading_3",
          "default": "t:sections.main-product.blocks.icon_with_text.settings.heading_3.default",
          "label": "Third heading",
          "info": "Leave the heading label blank to hide the icon column."
        },
        {
          "type": "liquid",
          "id": "heading_3_liquid",
          "label": "Third heading liquid",
          "info": "Overrides Third heading above"
        },
        {
          "type": "select",
          "id": "heading_title_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            }
          ],
          "default": "h4",
          "label": "Heading title size"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    },
    {
      "type": "buy_bar",
      "name": "Buy bar",
      "limit": 1,
      "settings": [
        {
          "type": "color_scheme",
          "id": "color_scheme_buy_bar",
          "default": "scheme-1",
          "label": "t:sections.all.colors.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "Button style"
        },
        {
          "type": "header",
          "content": "Visibility"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        },
        {
          "type": "liquid",
          "id": "liquid_before",
          "label": "Insert liquid before block content"
        },
        {
          "type": "liquid",
          "id": "liquid_after",
          "label": "Insert liquid after block content"
        },
        {
          "type": "text",
          "id": "custom_css",
          "label": "Block custom classes"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_sticky_info",
      "default": true,
      "label": "t:sections.main-product.settings.enable_sticky_info.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.main-product.settings.header.content",
      "info": "t:sections.main-product.settings.header.info"
    },
    {
      "type": "select",
      "id": "media_size",
      "options": [
        {
          "value": "small",
          "label": "t:sections.main-product.settings.media_size.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.media_size.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-product.settings.media_size.options__3.label"
        }
      ],
      "default": "large",
      "label": "t:sections.main-product.settings.media_size.label",
      "info": "t:sections.main-product.settings.media_size.info"
    },
    {
      "type": "checkbox",
      "id": "constrain_to_viewport",
      "default": true,
      "label": "t:sections.main-product.settings.constrain_to_viewport.label"
    },
    {
      "type": "select",
      "id": "media_fit",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.main-product.settings.media_fit.options__1.label"
        },
        {
          "value": "cover",
          "label": "t:sections.main-product.settings.media_fit.options__2.label"
        }
      ],
      "default": "contain",
      "label": "t:sections.main-product.settings.media_fit.label"
    },
    {
      "type": "select",
      "id": "gallery_layout",
      "options": [
        {
          "value": "stacked",
          "label": "t:sections.main-product.settings.gallery_layout.options__1.label"
        },
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.gallery_layout.options__2.label"
        },
        {
          "value": "thumbnail",
          "label": "t:sections.main-product.settings.gallery_layout.options__3.label"
        },
        {
          "value": "thumbnail_slider",
          "label": "t:sections.main-product.settings.gallery_layout.options__4.label"
        }
      ],
      "default": "stacked",
      "label": "t:sections.main-product.settings.gallery_layout.label"
    },
    {
      "type": "select",
      "id": "media_position",
      "options": [
        {
          "value": "left",
          "label": "t:sections.main-product.settings.media_position.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.main-product.settings.media_position.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.main-product.settings.media_position.label",
      "info": "t:sections.main-product.settings.media_position.info"
    },
    {
      "type": "select",
      "id": "image_zoom",
      "options": [
        {
          "value": "lightbox",
          "label": "t:sections.main-product.settings.image_zoom.options__1.label"
        },
        {
          "value": "hover",
          "label": "t:sections.main-product.settings.image_zoom.options__2.label"
        },
        {
          "value": "none",
          "label": "t:sections.main-product.settings.image_zoom.options__3.label"
        }
      ],
      "default": "lightbox",
      "label": "t:sections.main-product.settings.image_zoom.label",
      "info": "t:sections.main-product.settings.image_zoom.info"
    },
    {
      "type": "select",
      "id": "mobile_thumbnails",
      "options": [
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__1.label"
        },
        {
          "value": "show",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__2.label"
        },
        {
          "value": "hide",
          "label": "t:sections.main-product.settings.mobile_thumbnails.options__3.label"
        }
      ],
      "default": "hide",
      "label": "t:sections.main-product.settings.mobile_thumbnails.label"
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "default": false,
      "label": "t:sections.main-product.settings.hide_variants.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "t:sections.main-product.settings.enable_video_looping.label"
    },
    {
      "type": "header",
      "content": "Accordion settings"
    },
    {
      "type": "range",
      "id": "accordion_padding",
      "min": 0,
      "max": 10,
      "step": 0.5,
      "unit": "rem",
      "label": "Accordion padding",
      "info": "Sets left and right padding for collapsible rows",
      "default": 1
    },
    {
      "type": "select",
      "id": "accordion_title_size",
      "options": [
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        }
      ],
      "default": "h4",
      "label": "Accordion title size"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top_mobile",
      "default": 16
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom_mobile",
      "default": 16
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Custom column"
    },
    {
      "type": "liquid",
      "id": "custom_liquid_column",
      "label": "Custom column liquid"
    },
    {
      "type": "select",
      "id": "custom_column_placement",
      "options": [
        {
          "value": "first",
          "label": "First"
        },
        {
          "value": "last",
          "label": "Last"
        }
      ],
      "default": "first",
      "label": "Custom column placement"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "Custom liquid after",
      "info": "Loads after outer <div>"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom CSS classes for top <div>",
      "info": "The highest level component we can pass CSS classes to"
    },
    {
      "type": "text",
      "id": "product__info_custom_css_class",
      "label": "Custom CSS classes for .product__info-container"
    }
  ]
}
{% endschema %}
