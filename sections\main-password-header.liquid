{% comment %}
  bs-add
  - new color scheme options
{% endcomment %}

<style type="text/css">
  .password-logo {
    max-width: {{ settings.logo_width }}px;
  }
</style>

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="password-header">
    {%- if settings.logo != blank -%}
      {%- assign logo_alt = settings.logo.alt | default: shop.name | escape -%}
      {%- assign logo_height = settings.logo_width | divided_by: settings.logo.aspect_ratio -%}
      {{
        settings.logo
        | image_url: width: 500
        | image_tag:
          class: 'password-logo',
          widths: '50, 100, 150, 200, 250, 300, 400, 500',
          width: settings.logo_width,
          height: logo_height,
          alt: logo_alt
      }}
    {%- else -%}
      <h1 class="h2">{{ shop.name }}</h1>
    {%- endif -%}

    {%- if shop.password_message != blank -%}
      <div class="password-content">
        {{ shop.password_message }}
      </div>
    {%- endif -%}

    <password-modal>
      <details class="password-modal modal">
        <summary class="modal__toggle" aria-haspopup="dialog">
          <div class="modal__toggle-open password-link link underlined-link">
            <span class="svg-wrapper">
              {{- 'icon-padlock.svg' | inline_asset_content -}}
            </span>
            {{ 'general.password_page.login_password_button' | t }}
          </div>
          <div class="modal__toggle-close" aria-hidden="true">
            <span class="svg-wrapper">
              {{- 'icon-close.svg' | inline_asset_content -}}
            </span>
          </div>
        </summary>
        <div
          class="modal__content"
          role="dialog"
          aria-labelledby="DialogHeading"
          aria-modal="true"
        >
          <div class="password-modal__content" tabindex="-1">
            <button type="button" class="modal__close-button link" aria-label="{{ 'accessibility.close' | t }}">
              <span class="svg-wrapper">
                {{- 'icon-close.svg' | inline_asset_content -}}
              </span>
            </button>
            <h2 class="password-modal__content-heading" id="DialogHeading">
              {{ 'general.password_page.login_form_heading' | t }}
            </h2>
            {%- form 'storefront_password', class: 'password-form' -%}
              <div class="password-field field{% if form.errors %} password-field--error{% endif %}">
                <input
                  type="password"
                  name="password"
                  id="Password"
                  class="field__input"
                  autocomplete="current-password"
                  {% if form.errors %}
                    aria-invalid="true"
                    aria-describedby="PasswordLoginForm-password-error"
                  {%- endif -%}
                  placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}"
                >
                <label class="field__label" for="{{ 'general.password_page.login_form_password_label' | t }}">
                  {{- 'general.password_page.login_form_password_placeholder' | t -}}
                </label>
                {%- if form.errors -%}
                  <div id="PasswordLoginForm-password-error" role="status">
                    <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
                    <span class="form__message">
                      <span class="svg-wrapper">
                        {{- 'icon-error' | inline_asset_content -}}
                      </span>
                      {{ 'general.password_page.login_form_error' | t -}}
                    </span>
                  </div>
                {%- endif -%}
              </div>
              <button name="commit" class="password-button button button--outline">
                {{ 'general.password_page.login_form_submit' | t }}
              </button>
            {%- endform -%}
            <small class="password__footer-text">{{ 'general.password_page.admin_link_html' | t }}</small>
          </div>
        </div>
      </details>
    </password-modal>
  </div>
</div>
{% assign scheme1 = settings.color_schemes | first %}
{%- if section.settings.color_scheme == scheme1 -%}<hr>{%- endif -%}

{% schema %}
{
  "name": "t:sections.main-password-header.name",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.main-password-header.settings.logo_header.content"
    },
    {
      "type": "paragraph",
      "content": "t:sections.main-password-header.settings.logo_help.content"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    }
  ]
}
{% endschema %}