tab-component {
  position: relative;
  display: block;
}

tab-component button[aria-selected='true'].button--tertiary {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

tab-component button.button--tertiary:first-of-type {
  padding-left: 0;
}

tab-component button.button--tertiary:last-of-type {
  padding-right: 0;
}

@media screen and (max-width: 749px) {
  tab-component {
  }
}

@media screen and (min-width: 749px) and (max-width: 990px) {
  tab-component {
  }
}