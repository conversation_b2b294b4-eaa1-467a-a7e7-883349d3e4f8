<span class="field-currency">{{ cart.currency.symbol }}</span>

<div class="field">
  <input
    class="field__input"
    name="{{ filter.min_value.param_name }}"
    id="{{ id_prefix }}{{ filter.label | escape }}-GTE"
    {%- if filter.min_value.value -%}
      value="{{ filter.min_value.value | money_without_currency }}"
    {%- endif -%}
    type="text"
    inputmode="decimal"
    placeholder="0"
    data-pattern="\d| |,|\."
    data-min="0"
    data-max="{{ filter.range_max | money_without_currency }}"
  >
  <label class="field__label" for="{{ id_prefix }}{{ filter.label | escape }}-GTE">
    {{- 'products.facets.from' | t -}}
  </label>
</div>

{%- if filter_type != 'vertical' -%}
  <span class="field-currency">{{ cart.currency.symbol }}</span>
{%- endif -%}

<div class="field">
  <input
    class="field__input"
    name="{{ filter.max_value.param_name }}"
    id="{{ id_prefix }}{{ filter.label | escape }}-LTE"
    {%- if filter.max_value.value -%}
      value="{{ filter.max_value.value | money_without_currency }}"
    {%- endif -%}
    type="text"
    inputmode="decimal"
    placeholder="{{ filter.range_max | money_without_currency }}"
    data-pattern="\d| |,|\."
    data-min="0"
    data-max="{{ filter.range_max | money_without_currency }}"
  >
  <label class="field__label" for="{{ id_prefix }}{{ filter.label | escape }}-LTE">
    {{- 'products.facets.to' | t -}}
  </label>
</div>