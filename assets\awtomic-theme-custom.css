.awtomatic-progress-item.awtomatic-progress-item {
  background-color: var(--progress-bar-bg-color);
}
.awtomatic-progress-item.awtomatic-progress-item--completed {
  background-color: var(--progress-bar-completed-bg-color);
}
.bundle-stepper-item--complete:after {
  border-bottom-color: var(--progress-bar-completed-bg-color);
}
.bundle-stepper-item:after {
  border-bottom-color: var(--progress-bar-bg-color);
}

.bundle-stepper-counter--complete {
  color: var(--progress-bar-completed-color);
  background-color: var(--progress-bar-completed-bg-color);
}

.bundle-stepper-counter--incomplete {
  background-color: var(--progress-bar-bg-color);
  color: var(--progress-bar-color);
}

/* CUSTOM PROGRESS BAR */
@media only screen and (min-width: 750px) {
  .awtomatic-progress-item--completed button.awtomatic-progress-remove-button {
    display: inline-block;
    position: absolute;
    top: -8px;
    right: -8px;
    height: 20px;
    width: 20px;
    min-height: 0;
    border-radius: 50%;
    background-color: #fff;
    padding: 0;
    border-radius: 50%;
    box-shadow: 0px 0px 1px #000000d6;
    background-image: url(https://bundle-public-assets.s3.amazonaws.com/closeIcons/close-svgrepo-com.svg);
    background-size: 16px;
    transition: box-shadow 0.2s;
    background-position: center;
  }
  .awtomatic-progress-item--completed button.awtomatic-progress-remove-button:hover {
    box-shadow: 0px 0px 1px 1px #0000006e;
  }
  .awtomatic-progress-item--completed button.awtomatic-progress-remove-button:after {
    content: none;
  }
  .awtomatic-progress-count-label {
    display: none;
  }

  .bundle-container .awtomatic-progress-container {
    margin-left: auto;
    padding-bottom: 20px;
  }

  .bundleapp-modal__container--complete-screen footer {
    /* BaB Footer */
    height: 80px;
  }

  .bundleapp-modal__container--complete-screen .bundle-container {
    /* BaB content */
    padding-top: 10px;
  }

  .bundle-container .awtomatic-bab-options-container {
    order: 0;
  }
  .bundle-container .bundle-scrollable-section {
    order: 2;
    row-gap: 1rem;
  }
  .bundle-container .awtomatic-progress-container {
    order: 1;
  }
  .bundle-container footer {
    order: 3;
  }

  .awtomatic-progress-item--completed .awtomatic-progress-product-count-container .bundle-text--subheading {
    margin-right: 10px;
  }

  .awtomatic-progress-item.awtomatic-progress-item--pending .bundle-text--subheading {
    color: #000;
  }

  .awtomatic-progress-item.awtomatic-progress-item--completed {
    background-color: #fff;
  }

  .awtomatic-progress-item .bundle-text--subheading {
    min-width: 50px;
    display: block;
    color: #fff;
  }

  .awtomatic-progress-container {
    display: block;
    text-align: center;
    max-width: 80%;
    margin: 0 auto;
  }
  div.awtomatic-progress-item {
    width: 45px;
    height: 45px;
    display: inline-block;
    background-image: url();
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    background-color: #eee;
    margin: 0 5px;
    border-radius: 5px;
    border: 2px solid #ddd;
  }

  /* CUSTOM PROGRESS IMAGES */
  div.awtomatic-progress-item.awtomatic-progress-item--completed {
    background-color: #eee;
    background-image: var(--awt-pb-img);
  }
}
/* END CUSTOM PROGRESS BAR */

/* buttons */
.bundle-button.button--primary {
  background-color: var(--primary-btn-disabled-bg-color);
  color: var(--primary-btn-disabled-color);
  border: var(--primary-btn-disabled-border);
  border-radius: var(--btn-border-radius);
  transition: background-color 0.3s, color 0.3s;
}
.awtomatic-bab-options-container .bundle-button {
  background-color: var(--unselected-btn-bg-color);
  color: var(--unselected-btn-color);
  border: var(--unselected-btn-border);
  border-radius: var(--btn-border-radius);
  transition: background-color 0.3s, color 0.3s;
}
.awtomatic-bab-options-container .bundle-button span {
  background-color: transparent;
  color: var(--primary-btn-disabled-color);
  transition: color 0.3s;
}
.bundle-button.button--primary:hover,
.awtomatic-bab-options-container .bundle-button:hover {
  background-color: var(--primary-btn-disabled-bg-color-hover);
  color: var(--primary-btn-disabled-color-hover);
  border: var(--primary-btn-disabled-border-hover);
}
.awtomatic-bab-options-container .bundle-button:hover span {
  color: var(--primary-btn-disabled-color-hover);
}
.bundle-button.button--primary:enabled,
.awtomatic-bab-options-container .bundle-button.selected {
  background-color: var(--primary-btn-bg-color);
  color: var(--primary-btn-color);
  border: var(--primary-btn-border);
  border-radius: var(--btn-border-radius);
  transition: background-color 0.3s, color 0.3s;
}
.bundle-product-card__quantity-button-container--button svg {
  fill: var(--primary-btn-color) !important;
}
.bundle-product-card__quantity-button-container--button:hover svg {
  fill: var(--primary-btn-color-hover);
}
.awtomatic-bab-options-container .bundle-button.selected span {
  color: var(--primary-btn-color);
}
.bundle-button.button--primary:enabled:hover,
.awtomatic-bab-options-container .bundle-button:hover {
  cursor: pointer;
  background-color: var(--primary-btn-bg-color-hover);
  color: var(--primary-btn-color-hover);
  border: var(--primary-btn-border-hover);
}
.awtomatic-bab-options-container .bundle-button:hover span {
  color: var(--primary-btn-color-hover);
}
/* end buttons*/

/* Customer quantity button */
.bundle-product-card__quantity-button-container--unavailable button.bundle-product-card__quantity-button-container--button {
  padding: 0;
  width: 0;
}

div.bundle-product-card__quantity-button-container {
  /*border-color: #000586;*/
  border-width: 0;
  border-radius: var(--btn-border-radius);
  background-color: var(--primary-btn-bg-color);
  color: var(--primary-btn-color);
  font-weight: bold;
  line-height: 15px;
  padding: 0;
}

.bundle-product-card__quantity-button-container span {
  padding: 14px 10px;
}

.bundle-product-card__quantity-button-container--button {
  padding: 14px 10px;
  border-radius: 0;
  transition: background-color 0.3s;
}

.bundle-product-card__quantity-button-container--button:first-child {
  border-top-left-radius: var(--btn-border-radius);
  border-bottom-left-radius: var(--btn-border-radius);
}
.bundle-product-card__quantity-button-container--button:last-child {
  border-top-right-radius: var(--btn-border-radius);
  border-bottom-right-radius: var(--btn-border-radius);
}

button.bundle-product-card__quantity-button-container--button:hover {
  background-color: var(--primary-btn-bg-color-hover);
}

.bundle-product-card__quantity-button-container--button svg {
  fill: var(--primary-btn-color);
}
/* End Customer quantity button */

/* 2 items per row in BaB */
@media only screen and (max-width: 750px) {
  main.bundle-scrollable-section,
  main .bundle-section {
    grid-template-columns: repeat(auto-fit, minmax(50%, 1fr));
    row-gap: 0;
  }

  .bundleapp-modal__container--complete-screen .bundle-product-card {
    width: 100%;
  }

  .bundle-lazy-image-container {
    width: 100%;
  }

  .bundle-product-card__quantity-button-container {
    width: 100%;
  }
}
/* end 2 items per row in BaB */

/* square images */
.bundleapp-modal__container--complete-screen .bundle-product-card {
  width: 300px;
  height: 100%;
}
@media screen and (max-width: 768px) {
  .bundleapp-modal__container--complete-screen .bundle-product-card {
    width: 300px;
  }
}
@media screen and (max-width: 758px) {
  .bundleapp-modal__container--complete-screen .bundle-product-card {
    width: 100%;
  }
}

.bundle-lazy-image-container {
  padding-bottom: 100%;
  border-radius: 0;
}
/* end square images */

body.awt-bab-open .bundleapp-modal__logo {
  background-image: var(--logo-image-url);
}

body.awt-bab-open #dummy-chat-button-iframe,
body.awt-bab-open #gtx-trans {
  display: none !important;
}

/* optional extra bab styles */
body.awt-bab-open .bundleapp-modal__container--complete-screen {
  top: 0;
  outline: none;
  box-shadow: none;
}

body.awt-bab-open .bundleapp-modal__container--complete-screen:focus-visited,
body.awt-bab-open #bundleapp-subscriptions-portal *:focus-visible {
  outline: none;
}

body.awt-bab-open .bundleapp-modal {
  background: none;
  top: 0px;
  z-index: 999;
  outline: none;
  box-shadow: none;
}

body.awt-bab-open .bundle-product-card.selected {
  position: relative;
}

body.awt-bab-open .bundle-product-card.selected:before {
  content: "IN BOX";
  position: absolute;
  top: 35px;
  right: 35px;
  font-size: 14px;
  z-index: 9;
  padding: 3px 9px;
  background-color: var(--primary-btn-bg-color);
  color: var(--primary-btn-color);
  border: var(--primary-btn-border);
  border-radius: var(--btn-border-radius);
  transition: background-color 0.3s, color 0.3s;
}

body.awt-bab-open.subscription-overlay-open .shopify-section-group-group-header {
  z-index: 1;
}
body.awt-bab-open:not(.subscription-overlay-open) .slide-panel-backdrop,
body.awt-bab-open:not(.subscription-overlay-open) .slide-panel-container {
  display: none;
}

/* Shift/Alex customizations - pdp widget */
.bundleapp-wrapper {
  font-size: 2rem !important;
  margin: 0 0 20px 0 !important;
}
.bundleapp-plan-selector-group,
.bundleapp-plan-selector-description {
  border-radius: 10px !important;
  max-width: 44rem;
}
.bundleapp-plan-selector-group,
.bundleapp-plan-selector-group--selected,
.bundleapp-plan-selector-description {
  border-color: #2b2d20 !important;
}
.bundleapp-plan-selector-group--selected,
.bundleapp-plan-selector-description {
  background: #2b2d20 !important;
  color: #d7dbc6 !important;
}
.bundleapp-plan-selector-group:not([data-one-time]) .bundleapp-plan-selector-plan {
  display: block !important;
}

/* Shift/Alex customizations - Build a Bundle modal */
.bundleapp-modal__container--complete-screen {
  background-color: rgb(215, 219, 198);
}
.bundleapp-modal__container--complete-screen #modal-title {
  font-size: 4rem;
  font-family: 'Acumin_Pro_Wide_Black';
  font-weight: 400;
  text-transform: uppercase;
  margin-bottom: 0;
}
.bundleapp-modal__container--complete-screen .bundle-section__nav {
  background-color: rgb(215, 219, 198);
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 2rem;
}
@media screen and (max-width: 768px) {
  .bundleapp-modal__container--complete-screen header {
    height: auto;
  }
  .bundleapp-modal__container--complete-screen .bundle-section__nav {
    grid-template-columns: 1fr;
  }
}
.bundleapp-modal__container--complete-screen .bundle-section__nav button {
  border: 1px solid #000;
  border-radius: 40px;
  height: 100%;
}
.bundleapp-modal__container--complete-screen .bundle-section__nav button:hover {
  font-weight: normal;
  opacity: 0.5;
}
.bundleapp-modal__container--complete-screen .bundle-section__title {
  background: rgb(var(--color-foreground));
  color: rgb(216, 219, 200);
  padding: 1rem 0;
}
.bundleapp-modal__container--complete-screen footer > div {
  border-top: 3px solid rgb(var(--color-foreground));
}