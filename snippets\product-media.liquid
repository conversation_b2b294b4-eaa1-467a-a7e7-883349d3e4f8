{% comment %}
  Renders product media

  Accepts:
  - media: {Object} Product Media object
  - loop: {Boolean} Enable video looping (optional)
  - variant_image: {Boolean} Whether or not media is associated with a variant

  Usage:
  {% render 'product-media',
    media: media,
    loop: section.settings.enable_video_looping,
    variant_image: true
  %}
{% endcomment %}

{%- if media.media_type == 'image' -%}
  <img
    class="global-media-settings global-media-settings--no-shadow{% if variant_image %} product__media-item--variant{% endif %}"
    srcset="
      {%- if media.preview_image.width >= 550 -%}{{ media.preview_image | image_url: width: 550 }} 550w,{%- endif -%}
      {%- if media.preview_image.width >= 1100 -%}{{ media.preview_image | image_url: width: 1100 }} 1100w,{%- endif -%}
      {%- if media.preview_image.width >= 1445 -%}{{ media.preview_image | image_url: width: 1445 }} 1445w,{%- endif -%}
      {%- if media.preview_image.width >= 1680 -%}{{ media.preview_image | image_url: width: 1680 }} 1680w,{%- endif -%}
      {%- if media.preview_image.width >= 2048 -%}{{ media.preview_image | image_url: width: 2048 }} 2048w,{%- endif -%}
      {%- if media.preview_image.width >= 2200 -%}{{ media.preview_image | image_url: width: 2200 }} 2200w,{%- endif -%}
      {%- if media.preview_image.width >= 2890 -%}{{ media.preview_image | image_url: width: 2890 }} 2890w,{%- endif -%}
      {%- if media.preview_image.width >= 4096 -%}{{ media.preview_image | image_url: width: 4096 }} 4096w,{%- endif -%}
      {{ media.preview_image | image_url }} {{ media.preview_image.width }}w
    "
    sizes="(min-width: 750px) calc(100vw - 22rem), 1100px"
    src="{{ media.preview_image | image_url: width: 1445 }}"
    alt="{{ media.alt | escape }}"
    loading="lazy"
    width="1100"
    height="{{ 1100 | divided_by: media.preview_image.aspect_ratio | ceil }}"
    data-media-id="{{ media.id }}"
  >
{%- else -%}
  {%- if media.media_type == 'model' -%}
    <div class="product-media-modal__model" data-media-id="{{ media.id }}">
      <product-model
        class="deferred-media media media--transparent global-media-settings global-media-settings--no-shadow"
        style="padding-top: min(calc(100vh - 12rem), 100%)"
      >
  {%- else -%}
    <deferred-media
      class="deferred-media media global-media-settings global-media-settings--no-shadow"
      style="padding-top: min(calc(100vh - 12rem), {{ 1 | divided_by: media.aspect_ratio | times: 100 }}%)"
      data-media-id="{{ media.id }}"
    >
  {%- endif -%}

  <button id="Deferred-Poster-Modal-{{ media.id }}" class="deferred-media__poster" type="button">
    <span class="deferred-media__poster-button motion-reduce">
      {%- if media.media_type == 'model' -%}
        <span class="svg-wrapper">{{ 'icon-3d-model' | inline_asset_content }}</span>
      {%- else -%}
        <span class="svg-wrapper">{{ 'icon-play.svg' | inline_asset_content }}</span>
      {%- endif -%}
    </span>
    <img
      srcset="
        {% if media.preview_image.width >= 288 %}{{ media.preview_image | image_url: width: 288 }} 288w,{% endif %}
        {% if media.preview_image.width >= 576 %}{{ media.preview_image | image_url: width: 576 }} 576w,{% endif %}
        {% if media.preview_image.width >= 550 %}{{ media.preview_image | image_url: width: 550 }} 550w,{% endif %}
        {% if media.preview_image.width >= 1100 %}{{ media.preview_image | image_url: width: 1100 }} 1100w,{% endif %}
        {{ media.preview_image | image_url }} {{ media.preview_image.width }}w
      "
      src="{{ media | image_url: width: 550, height: 550 }}"
      sizes="(min-width: 1200px) calc((1200px - 10rem) / 2), (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)"
      loading="lazy"
      width="576"
      height="{{ 576 | divided_by: media.preview_image.aspect_ratio }}"
      alt="{{ media.preview_image.alt | escape }}"
    >
  </button>
  <template>
    {%- case media.media_type -%}
      {%- when 'external_video' -%}
        {%- assign video_class = 'js-' | append: media.host -%}
        {%- if media.host == 'youtube' -%}
          {{
            media
            | external_video_url: autoplay: true, loop: loop, playlist: media.external_id
            | external_video_tag: class: video_class, loading: 'lazy'
          }}
        {%- else -%}
          {{
            media
            | external_video_url: autoplay: true, loop: loop
            | external_video_tag: class: video_class, loading: 'lazy'
          }}
        {%- endif -%}
      {%- when 'video' -%}
        {{ media | media_tag: image_size: '2048x', autoplay: true, loop: loop, controls: true, preload: 'none' }}
      {%- when 'model' -%}
        {{ media | media_tag: image_size: '2048x', toggleable: true }}
    {%- endcase -%}
  </template>

  {%- if media.media_type == 'model' -%}
    </product-model>
    <button
      class="button button--full-width product__xr-button"
      type="button"
      aria-label="{{ 'products.product.xr_button_label' | t }}"
      data-shopify-xr
      data-shopify-model3d-id="{{ media.id }}"
      data-shopify-title="title"
      data-shopify-xr-hidden
    >
      <span class="svg-wrapper">{{ 'icon-3d-model' | inline_asset_content }}</span>
      {{ 'products.product.xr_button' | t }}
    </button>
    </div>
  {%- else -%}
    </deferred-media>
  {%- endif -%}
{%- endif -%}