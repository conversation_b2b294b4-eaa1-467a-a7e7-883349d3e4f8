if (!customElements.get('color-swatch')) {
  customElements.define('color-swatch', class ColorSwatch extends HTMLElement {
    constructor() {
      super();

      this.variantId = this.dataset.variantId;
      this.container = this.closest('.product-card-wrapper');

      if (this.variantId) {
        this.addEventListener('click', this.onClickHandler.bind(this));
      }
    }

    onClickHandler() {
      this.colorSwatchChange();
      this.updateUrl();
    }

    colorSwatchChange() {
      const image = this.container.querySelector('.media img');

      if (image !== null) {
        if (image !== null) {
          image.src = this.dataset.src;
          image.srcset = this.dataset.srcset;
        }

        const swatches = this.container.querySelectorAll('.color-swatch');
        swatches.forEach((swatch) => {
          swatch.setAttribute('aria-selected', false);
        });

        this.setAttribute('aria-selected', true);
      }
    }

    updateUrl() {
      const destinations = this.container.querySelectorAll('a, button[data-product-url]');
      destinations.forEach((link) => {
        if (link.hasAttribute('href'))
          link.setAttribute('href', this.dataset.url);
        else
          link.setAttribute('data-product-url', this.dataset.url);
      });
    }
  });
}