/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "logo": "shopify://shop_images/shift-naturals-logo-4123.png",
    "logo_width": 180,
    "favicon": "shopify://shop_images/downshift_ico.svg",
    "type_header_font": "assistant_n4",
    "heading_scale": 100,
    "type_body_font": "assistant_n4",
    "body_scale": 100,
    "custom_fonts": "/* New Shift File */\n\n/* Acumin_Pro_Wide_Black */\n@font-face { \n  font-family: 'acumin_pro_wide_black'; \n  src: \n    url(\"{{ 'acumin_pro_wide_black.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: normal;\n  font-display: swap;\n}\n@font-face { \n  font-family: 'acumin_pro_wide_black'; \n  src: \n    url(\"{{ 'acumin_pro_wide_black_italic.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: italic;\n  font-display: swap;\n}\n\n/* Acumin_Pro_Wide_Bold */\n@font-face { \n  font-family: 'acumin_pro_wide_bold'; \n  src: \n    url(\"{{ 'acumin_pro_wide_bold.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: normal;\n  font-display: swap;\n}\n@font-face { \n  font-family: 'acumin_pro_wide_bold'; \n  src: \n    url(\"{{ 'acumin_pro_wide_bold_italic.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: italic;\n  font-display: swap;\n}\n\n/* Acumin_Pro_SemiCond_Semibold */\n@font-face { \n  font-family: 'acumin_pro_semicond_semibold'; \n  src: \n    url(\"{{ 'acumin_pro_semicond_semibold.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: normal;\n  font-display: swap;\n}\n@font-face { \n  font-family: 'acumin_pro_semicond_semibold'; \n  src: \n    url(\"{{ 'acumin_pro_semicond_semibold_italic.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: italic;\n  font-display: swap;\n}\n\n/* Acumin_Pro_Cond_Semibold */\n@font-face { \n  font-family: 'acumin_pro_cond_semibold'; \n  src: \n    url(\"{{ 'acumin_pro_cond_semibold.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: normal;\n  font-display: swap;\n}\n@font-face { \n  font-family: 'acumin_pro_cond_semibold'; \n  src: \n    url(\"{{ 'acumin_pro_cond_semibold_italic.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: italic;\n  font-display: swap;\n}\n\n\n/* Acumin_Pro */\n@font-face { \n  font-family: 'acumin_pro'; \n  src: \n    url(\"{{ 'acumin_pro_book.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: normal;\n  font-display: swap;\n}\n@font-face { \n  font-family: 'acumin_pro'; \n  src: \n    url(\"{{ 'acumin_pro_book_italic.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: italic;\n  font-display: swap;\n}\n@font-face { \n  font-family: 'acumin_pro'; \n  src: \n    url(\"{{ 'acumin_pro_bold.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: bold;\n  font-style: normal;\n  font-display: swap;\n}\n@font-face { \n  font-family: 'acumin_pro'; \n  src: \n    url(\"{{ 'acumin_pro_bold_italic.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: bold;\n  font-style: italic;\n  font-display: swap;\n}\n\n/* acumin_pro_cond_black - Buttons, Menu items */\n@font-face { \n  font-family: 'acumin_pro_cond'; \n  src: \n    url(\"{{ 'acumin_pro_cond_black.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: normal;\n  font-display: swap;\n}\n@font-face { \n  font-family: 'acumin_pro_cond'; \n  src: \n    url(\"{{ 'acumin_pro_cond_black_italic.woff2' | file_url }}\") format(\"woff2\");\n  font-weight: normal;\n  font-style: italic;\n  font-display: swap;\n}\n\n\n:root {\n  --hxxl-font-size: clamp(calc(var(--font-heading-scale) * 5.6rem), 14vw, calc(var(--font-heading-scale) * 7.2rem));\n  --hxxl-line-height: 1;\n  --hxxl-letter-spacing: -0.05em;\n\n  --hxl-font-size: calc(var(--font-heading-scale) * 4.2rem);\n  --hxl-line-height: calc(var(--font-heading-scale) * 1);\n  --hxl-letter-spacing: 0;\n\n  --h0-font-size: calc(var(--font-heading-scale) * 3.6rem);\n  --h0-line-height: calc(var(--font-heading-scale) * 1);\n  --h0-letter-spacing: 0;\n\n  --h1-font-size: calc(var(--font-heading-scale) * 3.2rem);\n  --h1-line-height: calc(var(--font-heading-scale) * 1.1);\n  --h1-letter-spacing: 0;\n\n  --h2-font-size: calc(var(--font-heading-scale) * 2.4rem);\n  --h2-line-height: calc(var(--font-heading-scale) * 1.1);\n  --h2-letter-spacing: 0;\n\n  --h3-font-size: calc(var(--font-heading-scale) * 2rem);\n  --h3-line-height: calc(var(--font-heading-scale) * 1.1);\n  --h3-letter-spacing: 0;\n\n  --h4-font-size: calc(var(--font-heading-scale) * 1.6rem);\n  --h4-line-height: calc(var(--font-heading-scale) * 1.1);\n  --h4-letter-spacing: 0;\n\n  --h5-font-size: calc(var(--font-heading-scale) * 1.6rem);\n  --h5-line-height: calc(var(--font-heading-scale) * 1.1);\n  --h5-letter-spacing: 0;\n\n  --h6-font-size: calc(var(--font-heading-scale) * 1.6rem);\n  --h6-line-height: calc(var(--font-heading-scale) * 1.1);\n  --h6-letter-spacing: 0;\n\n  --body-copy-large-font-size: calc(var(--font-heading-scale) * 1.8rem);\n  --body-copy--large-line-height: calc(var(--font-heading-scale) * 1.2);\n  --body-copy-large-letter-spacing: 0;\n\n  --body-copy-font-size: calc(var(--font-heading-scale) * 1.5rem);\n  --body-copy-line-height: calc(var(--font-heading-scale) * 1.35);\n  --body-copy-letter-spacing: 0;\n\n  --body-copy-small-font-size: calc(var(--font-heading-scale) * 1.3rem);\n  --body-copy--small-line-height: calc(var(--font-heading-scale) * 1.35);\n  --body-copy-small-letter-spacing: 0;\n}\n\n@media screen and (min-width: 750px) {\n  :root {\n    --hxxl-font-size: calc(var(--font-heading-scale) * 9.6rem);\n    --hxxl-line-height: calc(var(--font-heading-scale) * 1);\n    --hxxl-letter-spacing: 0;\n\n    --hxl-font-size: calc(var(--font-heading-scale) * 9.6rem);\n    --hxl-line-height: calc(var(--font-heading-scale) * 1);\n    --hxl-letter-spacing: 0;\n\n    --h0-font-size: calc(var(--font-heading-scale) * 8.2rem);\n    --h0-line-height: calc(var(--font-heading-scale) * 1);\n    --h0-letter-spacing: 0;\n\n    --h1-font-size: calc(var(--font-heading-scale) * 6.4rem);\n    --h1-line-height: calc(var(--font-heading-scale) * 1.1);\n    --h1-letter-spacing: 0;\n\n    --h2-font-size: calc(var(--font-heading-scale) * 4.8rem);\n    --h2-line-height: calc(var(--font-heading-scale) * 1.1);\n    --h2-letter-spacing: 0;\n\n    --h3-font-size: calc(var(--font-heading-scale) * 3.2rem);\n    --h3-line-height: calc(var(--font-heading-scale) * 1.1);\n    --h3-letter-spacing: 0;\n\n    --h4-font-size: calc(var(--font-heading-scale) * 2.4rem);\n    --h4-line-height: calc(var(--font-heading-scale) * 1.1);\n    --h4-letter-spacing: 0;\n\n    --h5-font-size: calc(var(--font-heading-scale) * 2.4rem);\n    --h5-line-height: calc(var(--font-heading-scale) * 1.1);\n    --h5-letter-spacing: 0;\n\n    --h6-font-size: calc(var(--font-heading-scale) * 2.4rem);\n    --h6-line-height: calc(var(--font-heading-scale) * 1.1);\n    --h6-letter-spacing: 0;\n\n    --body-copy-large-font-size: calc(var(--font-heading-scale) * 2rem);\n    --body-copy--large-line-height: calc(var(--font-heading-scale) * 1.4);\n    --body-copy-large-letter-spacing: 0;\n\n    --body-copy-font-size: calc(var(--font-heading-scale) * 1.7rem);\n    --body-copy-line-height: calc(var(--font-heading-scale) * 1.4);\n    --body-copy-letter-spacing: 0;\n\n    --body-copy-small-font-size: calc(var(--font-heading-scale) * 1.3rem);\n    --body-copy--small-line-height: calc(var(--font-heading-scale) * 1.4);\n    --body-copy-small-letter-spacing: 0;\n  }\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\n.hxxl,\n.hxl,\n.h0,\n.h1,\n.h2,\n.h3,\n.h4,\n.h5,\n.h6 {\n  margin: 0;\n  font-family: 'Acumin_Pro_Wide_Black';\n  font-weight: 400;\ntext-transform: uppercase;\n}\n\nh1 {\n  font-size: var(--h1-font-size);\n  line-height: var(--h1-line-height);\n  letter-spacing: var(--h1-letter-spacing);\n}\n\nh2 {\n  font-size: var(--h2-font-size);\n  line-height: var(--h2-line-height);\n  letter-spacing: var(--h2-letter-spacing);\n}\n\nh3 {\n  font-size: var(--h3-font-size);\n  line-height: var(--h3-line-height);\n  letter-spacing: var(--h3-letter-spacing);\n}\n\nh4 {\n  font-size: var(--h4-font-size);\n  line-height: var(--h4-line-height);\n  letter-spacing: var(--h4-letter-spacing);\n}\n\nh5 {\n  font-size: var(--h5-font-size);\n  line-height: var(--h5-line-height);\n  letter-spacing: var(--h5-letter-spacing);\n  font-family: 'Acumin_Pro_SemiCond_Semibold', sans-serif;\n}\n\nh6 {\n  font-size: var(--h6-font-size);\n  line-height: var(--h6-line-height);\n  letter-spacing: var(--h6-letter-spacing);\n  color: rgba(var(--color-foreground), 0.8);\n  font-family: 'Acumin_Pro_SemiCond_Semibold', sans-serif;\n}\n\n.hxxl {\n  font-size: var(--hxxl-font-size);\n  line-height: var(--hxxl-line-height);\n  letter-spacing: var(--hxxl-letter-spacing);\n}\n\n.hxl {\n  font-size: var(--hxl-font-size);\n  line-height: var(--hxl-line-height);\n  letter-spacing: var(--hxl-letter-spacing);\n}\n\n.h0 {\n  font-size: var(--h0-font-size);\n  line-height: var(--h0-line-height);\n  letter-spacing: var(--h0-letter-spacing);\n}\n\n.h1 {\n  font-size: var(--h1-font-size);\n  line-height: var(--h1-line-height);\n  letter-spacing: var(--h1-letter-spacing);\n}\n\n.h2 {\n  font-size: var(--h2-font-size);\n  line-height: var(--h2-line-height);\n  letter-spacing: var(--h2-letter-spacing);\n}\n\n.h3,\n.price--large,\n.multicolumn .multicolumn-list h3,\n.cart__empty-text,\n.drawer__heading {\n  font-size: var(--h3-font-size);\n  line-height: var(--h3-line-height);\n  letter-spacing: var(--h3-letter-spacing);\n}\n\n.h4 {\n  font-size: var(--h4-font-size);\n  line-height: var(--h4-line-height);\n  letter-spacing: var(--h4-letter-spacing);\n}\n\n.h5 {\n  font-size: var(--h5-font-size);\n  line-height: var(--h5-line-height);\n  letter-spacing: var(--h5-letter-spacing);\n  font-family: 'Acumin_Pro_SemiCond_Semibold', sans-serif;\n}\n\n.h6 {\n  font-size: var(--h6-font-size);\n  line-height: var(--h6-line-height);\n  letter-spacing: var(--h6-letter-spacing);\n  font-family: 'Acumin_Pro_SemiCond_Semibold', sans-serif;\n}\n\n.body-copy-large,\n.subtitle,\n.accordion .accordion__title {\n  font-size: var(--body-copy-large-font-size);\n  line-height: var(--body-copy-large-line-height);\n  letter-spacing: var(--body-copy-large-letter-spacing);\n  font-family: 'Acumin_Pro';\n}\n\nbody,\n.text-body,\n.body-copy {\n  font-size: var(--body-copy-font-size);\n  line-height: var(--body-copy-line-height);\n  letter-spacing: var(--body-copy-letter-spacing);\n  margin: 0;\n  font-family: 'Acumin_Pro';\n}\n\n\n.body-copy-small,\n.caption,\n.caption-large,\n.customer .field input,\n.customer select,\n.field__input,\n.form__label,\n.select__select {\n  font-size: var(--body-copy-small-font-size);\n  line-height: var(--body-copy-small-line-height);\n  letter-spacing: var(--body-copy-small-letter-spacing);\n  color: rgba(var(--color-foreground), 1);\n  font-family: 'Acumin_Pro';\n}\n\n.light {\n  opacity: 0.75;\n}",
    "page_width": 1500,
    "spacing_sections": 0,
    "spacing_grid_horizontal_mobile": 8,
    "spacing_grid_horizontal": 10,
    "spacing_grid_vertical_mobile": 8,
    "spacing_grid_vertical": 10,
    "menu_drawer": "main-v2-drawer",
    "search_display": "none",
    "account_display": "text",
    "cart_display": "text",
    "animations_reveal_on_scroll": true,
    "animations_hover_elements": "vertical-lift",
    "buttons_border_thickness": 1,
    "buttons_border_opacity": 100,
    "buttons_radius": 40,
    "buttons_shadow_opacity": 0,
    "buttons_shadow_horizontal_offset": 0,
    "buttons_shadow_vertical_offset": 4,
    "buttons_shadow_blur": 5,
    "button-custom-css": ".button, .shopify-challenge__button, .customer button, button.shopify-payment-button__button--unbranded {\n    font-family: var(--font-heading-family);\n    font-style: var(--font-heading-style);\n    font-weight: var(--font-heading-weight);\n    letter-spacing: calc(var(--font-heading-scale) * 0.06rem);\n  text-transform: uppercase;\n}",
    "button-1-background-color": "#ffffff",
    "button-1-custom-css": ".button-1 {\n  background-color: transparent;\n}",
    "button-2-background-color": "#98de59",
    "button-2-border-color": "#98de59",
    "variant_pills_border_thickness": 1,
    "variant_pills_border_opacity": 55,
    "variant_pills_radius": 6,
    "variant_pills_shadow_opacity": 0,
    "variant_pills_shadow_horizontal_offset": 0,
    "variant_pills_shadow_vertical_offset": 4,
    "variant_pills_shadow_blur": 5,
    "variant_color_pills_radius": 0,
    "inputs_border_thickness": 2,
    "inputs_border_opacity": 100,
    "inputs_radius": 10,
    "inputs_shadow_opacity": 0,
    "inputs_shadow_horizontal_offset": 0,
    "inputs_shadow_vertical_offset": 4,
    "inputs_shadow_blur": 5,
    "card_style": "standard",
    "card_image_padding": 0,
    "card_text_alignment": "center",
    "card_color_scheme": "scheme-4",
    "card_image_shape": "default",
    "card_show_rating": false,
    "card_price_bottom": false,
    "card_border_thickness": 0,
    "card_border_opacity": 10,
    "card_corner_radius": 12,
    "card_shadow_opacity": 0,
    "card_shadow_horizontal_offset": 0,
    "card_shadow_vertical_offset": 4,
    "card_shadow_blur": 5,
    "collection_card_style": "standard",
    "collection_card_image_padding": 0,
    "collection_card_text_alignment": "left",
    "collection_card_color_scheme": "scheme-2",
    "collection_card_border_thickness": 0,
    "collection_card_border_opacity": 10,
    "collection_card_corner_radius": 0,
    "collection_card_shadow_opacity": 0,
    "collection_card_shadow_horizontal_offset": 0,
    "collection_card_shadow_vertical_offset": 4,
    "collection_card_shadow_blur": 5,
    "blog_card_style": "standard",
    "blog_card_image_padding": 0,
    "blog_card_text_alignment": "left",
    "blog_card_color_scheme": "scheme-2",
    "blog_card_border_thickness": 0,
    "blog_card_border_opacity": 10,
    "blog_card_corner_radius": 0,
    "blog_card_shadow_opacity": 0,
    "blog_card_shadow_horizontal_offset": 0,
    "blog_card_shadow_vertical_offset": 4,
    "blog_card_shadow_blur": 5,
    "text_boxes_border_thickness": 0,
    "text_boxes_border_opacity": 10,
    "text_boxes_radius": 10,
    "text_boxes_shadow_opacity": 0,
    "text_boxes_shadow_horizontal_offset": 0,
    "text_boxes_shadow_vertical_offset": 4,
    "text_boxes_shadow_blur": 5,
    "media_border_thickness": 0,
    "media_border_opacity": 5,
    "media_radius": 0,
    "media_shadow_opacity": 0,
    "media_shadow_horizontal_offset": 0,
    "media_shadow_vertical_offset": 4,
    "media_shadow_blur": 5,
    "popup_border_thickness": 1,
    "popup_border_opacity": 10,
    "popup_corner_radius": 0,
    "popup_shadow_opacity": 5,
    "popup_shadow_horizontal_offset": 0,
    "popup_shadow_vertical_offset": 4,
    "popup_shadow_blur": 5,
    "drawer_border_thickness": 1,
    "drawer_border_opacity": 10,
    "drawer_shadow_opacity": 0,
    "drawer_shadow_horizontal_offset": 0,
    "drawer_shadow_vertical_offset": 4,
    "drawer_shadow_blur": 5,
    "badge_position": "bottom left",
    "badge_corner_radius": 40,
    "sale_badge_color_scheme": "scheme-7e1a7ee4-b3ff-4c39-9666-ccf40e8910c5",
    "sold_out_badge_color_scheme": "scheme-6",
    "product_image_badge_color_scheme": "scheme-1",
    "social_facebook_link": "",
    "social_instagram_link": "https://www.instagram.com/enjoyshift/",
    "social_youtube_link": "https://www.youtube.com/@EnjoyShift",
    "social_tiktok_link": "https://www.tiktok.com/@enjoyshift",
    "social_twitter_link": "",
    "social_snapchat_link": "",
    "social_pinterest_link": "",
    "social_tumblr_link": "",
    "social_vimeo_link": "",
    "predictive_search_enabled": true,
    "predictive_search_show_vendor": false,
    "predictive_search_show_price": false,
    "currency_code_enabled": false,
    "cart_type": "drawer",
    "show_vendor": false,
    "show_cart_note": false,
    "cart_button_style": "button button--secondary",
    "cart_product_title_font_size": "h6",
    "cart_drawer_collection": "",
    "cart_color_scheme": "scheme-2",
    "global_custom_css": ".button,\n.shopify-challenge__button,\n.customer button,\nbutton.shopify-payment-button__button--unbranded {\n  --font-heading-family: acumin_pro_cond, sans-serif;\n  font-size: 2.2rem;\n}\n\n.button__small {\n  font-size: 1.8rem;\n  min-width: calc(8rem + var(--buttons-border-width) * 2);\n  min-height: calc(3.5rem + var(--buttons-border-width) * 2);\n}\n\n.link {\nfont-size: inherit;\n}\n\na {\ncolor: inherit;\nfont-size: inherit;\n}\n\n.rte ul,\n.rte ol {\n  list-style-position: outside;\n  padding-left: 3rem;\n}\n\n/* cart*/\n.cart__ctas {\n  margin-top: 1rem;\n}\n\n.banner__box,\n.textWrapBalance {\n  text-wrap: balance;\n}\n\n.caption:not(.footer__copyright) {\n  font-size: 2rem;\n}\n\n@media screen and (max-width: 749px) {\n  .caption:not(.footer__copyright) {\n    font-size: 1.4rem;\n  }\n}\n\n.bon-image {\n  width: 100%;\n  height: auto;\n}\n\n/* footer */\n@media screen and (min-width: 750px) {\n  .footer__content-bottom-wrapper > div:last-of-type {\n    display: grid;\n    align-content: center;\n  }\n}\n\n.bon-footer-header--newsletter {\n  color: rgb(var(--bon-highlight-color--light));\n  font-size: 22px; /* custom font size that is not part of style guide */\n}\n@media screen and (max-width: 749px) {\n  .footer__content-top .footer__blocks-wrapper > div:first-of-type {\n    margin: 1rem 0 2.5rem;\n  }\n}\n\n/* utils */\n:root {\n  --bon-highlight-color--light: 151, 222, 89; /* #98DE59 */\n  --bon-color-olive: 124, 132, 89; /* #7C8459 */\n  --bon-color-orange: 224, 101, 7; /* #e06507 */\n}\n.bon-olive-color {\n  color: rgb(var(--bon-color-olive));\n}\n.bon-highlight-color--light {\n  color: rgb(var(--bon-highlight-color--light));\n}\n.bon-text-justify {\n  text-align: justify;\n  text-justify: inter-word;\n}\n.bon-line-height-125 {\n  line-height: 1.2;\n}\n\n/* collapsible content section customizations */\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__container\n  .accordion__tabs-button {\n  opacity: 0.6;\n}\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__container\n  .accordion__tabs-button[active],\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__container\n  .accordion__tabs-button:hover {\n  transition: opacity var(--duration-short) ease;\n  opacity: 1;\n}\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__container\n  .accordion__tabs-button[active]\n  .accordion__tabs-button__title {\n  text-decoration: none;\n}\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__container\n  .accordion__tabs-button\n  .accordion__tabs-button__title {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n.accordion__tabs-button__title .bon-highlight-color--light {\n  margin-bottom: 1.2rem;\n}\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__container\n  .accordion__tabs-button {\n  border-bottom-width: 0.2rem;\n  padding-right: 2rem;\n}\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__container\n  .accordion__tabs-button[active] {\n  border-bottom-color: rgba(var(--color-foreground), 1);\n}\n.shopify-section.section.section--collapsible-content .accordion__tabs {\n  gap: 1rem;\n}\n.accordion__tabs__container .accordion__tabs__content {\n  border-bottom-color: rgba(var(--color-foreground), 1);\n  border-bottom-width: 0.2rem;\n}\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__content {\n  padding-top: 5rem;\n}\n.shopify-section.section.section--collapsible-content\n  .accordion__tabs__container\n  .accordion__content {\n  text-align: justify;\n  text-justify: inter-word;\n}\n\n.shopify-section.section.section--collapsible-content\n  .accordion__content_desktop_left\n  .bon-product-icon-split-list,\n.multicolumn-card__info__items .bon-product-icon-split-list {\n  color: rgba(var(--color-foreground), 1);\n  fill: rgba(var(--color-foreground), 1);\n}\n.shopify-section.section.section--collapsible-content\n  .accordion__content_desktop_left\n  .bon-product-icon-split-list,\n.multicolumn-card__info__items .bon-product-icon-split-list path {\n  fill: rgba(var(--color-foreground), 1);\n}\n\n@media screen and (min-width: 750px) {\n  .shopify-section.section.section--collapsible-content\n    .accordion__tabs__content {\n    margin-top: -0.2rem;\n    padding-top: 10rem;\n    border-top: 0.2rem solid rgba(var(--bon-color-olive), 1); /* rgba(var(--bon-color-olive), 1);*/\n  }\n  .shopify-section.section.section--collapsible-content .accordion__content {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 4rem;\n  }\n  .shopify-section.section.section--collapsible-content\n    .accordion__content_desktop_left,\n  .shopify-section.section.section--collapsible-content\n    .accordion__content_desktop_right {\n    width: 100%;\n    padding: 0;\n  }\n  .shopify-section.section.section--collapsible-content\n    .accordion__content_desktop_left\n    > *:first-child {\n    margin-top: 0;\n  }\n}\n\n\n/* popup page customize */\n.product-popup-modal__content-info > h1,\n.product-popup-modal__content-info > h2,\n.product-popup-modal__content-info > h3,\n.product-popup-modal__content-info > h4,\n.product-popup-modal__content-info > h5,\n.product-popup-modal__content-info > h6 {\n  margin-bottom: 0.5rem;\n}\n.product-popup-modal__content-info > p:first-of-type {\n  margin-top: 2.5rem;\n}\n.product-popup-modal__content-info > p {\n  margin-bottom: 1.5rem;\n}\n.product-popup-modal__content-info {\n  margin-bottom: 5rem;\n}\n.product-popup-modal__content > .product-popup-modal__toggle {\n  border: 0.1rem solid rgba(var(--color-foreground), 1);\n  color: rgba(var(--color-foreground), 1);\n}\n\n.product-popup-modal__content-info iframe {\n  margin-top: 2.5rem;\n  aspect-ratio: 16 / 9;\n}\n\n/* policy pages */\n.shopify-policy__container {\n  margin: 0 auto;\n  padding: 0 1.5rem;\n  max-width: 100%;\n}\n.shopify-policy__title h1 {\n  font-size: 4rem;\n  text-align: left;\n  margin-top: 5rem;\n  margin-bottom: 3rem;\n}\n@media screen and (max-width: 749px) {\n  .shopify-policy__title h1 {\n    font-size: 3.2rem;\n  }\n}\n.shopify-policy__body {\n  margin-bottom: 5rem;\n}\n@media screen and (min-width: 750px) {\n  .shopify-policy__container {\n    padding: 0 9rem;\n  }\n  .shopify-policy__title h1 {\n    font-size: 5.2rem;\n    margin-bottom: 4rem;\n  }\n}\n@media screen and (min-width: 990px) {\n  .shopify-policy__container {\n    max-width: 72.6rem;\n    padding: 0;\n  }\n}\n\n/* image list */\n.bon-product-detail-list {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-top: 0.2rem solid;\n  border-bottom: 0.2rem solid;\n  padding: 1rem 0;\n}\n.bon-product-detail-list.bon-product-detail-list__light-borders {\n  border-top: 0.1rem solid;\n  border-bottom: 0.1rem solid;\n}\n\n/* accordion overrides */\n.accordion__title {\n  font-family: var(--font-body-family);\n  font-style: var(--font-body-style);\n  font-weight: var(--font-body-weight);\n  font-size: calc(var(--font-body-scale) * 2rem);\n  text-transform: capitalize;\n}\n\n/* age check side cart */\n.agpc__container {\n  color: rgb(var(--bon-color-orange)) !important;\n  border-color: rgb(var(--bon-color-orange)) !important;\n  background-color: transparent !important;\n  border-radius: 0 !important;\n  position: relative !important;\n}\n.agpc__container.agpc__container--checked {\n  color: rgb(var(--color-foreground)) !important;\n  border-color: rgb(var(--color-foreground)) !important;\n}\n.agpc__label {\n  font-family: var(--font-heading-family) !important;\n  text-transform: uppercase !important;\n}\n#agechecker_payment_buttons_checkbox:after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n\n/* section--multicolumn--terpine START */\n.section--multicolumn--terpine .multicolumn {\n  --grid-desktop-vertical-spacing: 0px;\n  --grid-desktop-horizontal-spacing: 0px;\n  --grid-mobile-vertical-spacing: 0px;\n  --grid-mobile-horizontal-spacing: 0px;\n  overflow: hidden;\n  background-color: transparent;\n}\n.section--multicolumn--terpine\n  .background-none\n  .multicolumn-list\n  .multicolumn-card__info,\n.section--multicolumn--terpine .multicolumn-card__image-wrapper,\n.section--multicolumn--terpine .multicolumn-list:not(.slider),\n.section--multicolumn--terpine\n  .background-none\n  .grid--2-col-tablet\n  .multicolumn-list__item {\n  margin: 0;\n  padding: 0;\n  grid-area: 1 / 1;\n}\n\n@media screen and (max-width: 899px) {\n  .section--multicolumn--terpine .enjoy-shift-custom-multicolumn {\n    max-height: 500px;\n  }\n  .section--multicolumn--terpine\n    .scroll-trigger:not(.scroll-trigger--offscreen).animate--slide-in {\n    animation-delay: 0s;\n  }\n}\n@media screen and (min-width: 990px) {\n  .section--multicolumn--terpine .enjoy-shift-custom-multicolumn {\n    max-height: 600px;\n  }\n}\n.section--multicolumn--terpine .enjoy-shift-custom-multicolumn {\n  position: relative;\n}\n.section--multicolumn--terpine .enjoy-shift-custom-multicolumn-content {\n  position: absolute;\n  bottom: 5rem;\n  left: 5rem;\n  right: 5rem;\n  z-index: 2;\n  text-decoration: none;\n}\n.section--multicolumn--terpine .enjoy-shift-custom-multicolumn img {\n  width: 100%;\n  height: 100%;\n}\n.section--multicolumn--terpine .enjoy-shift-custom-multicolumn:after {\n  background: linear-gradient(\n    0deg,\n    rgba(0, 0, 0, 0.76) 0%,\n    rgba(0, 0, 0, 0.3) 100%\n  );\n  z-index: 1;\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n}\n.section--multicolumn--terpine .enjoy-shift-custom-multicolumn-content .link {\n  text-transform: uppercase;\n  color: rgb(var(--bon-highlight-color--light));\n}\n@media (hover: hover) {\n  .section--multicolumn--terpine .enjoy-shift-custom-multicolumn-content {\n    animation: var(--animation-slide-in);\n  }\n  .section--multicolumn--terpine .enjoy-shift-custom-multicolumn:after {\n    content: none;\n  }\n  .section--multicolumn--terpine\n    .enjoy-shift-custom-multicolumn\n    .enjoy-shift-custom-multicolumn-content {\n    display: none;\n  }\n  .section--multicolumn--terpine .enjoy-shift-custom-multicolumn:hover:after {\n    background: linear-gradient(\n      0deg,\n      rgba(0, 0, 0, 0.76) 0%,\n      rgba(0, 0, 0, 0.3) 100%\n    );\n    z-index: 1;\n    content: \"\";\n    position: absolute;\n    width: 100%;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n    transition: scale var(--duration-medium) var(--ease-out-slow),\n      filter var(--duration-medium) var(--ease-out-slow);\n  }\n  .section--multicolumn--terpine\n    .enjoy-shift-custom-multicolumn:hover\n    .enjoy-shift-custom-multicolumn-content {\n    display: block;\n  }\n  .section--multicolumn--terpine .enjoy-shift-custom-multicolumn:hover img {\n    --zoom-in-ratio: 1.15;\n    transition: scale var(--duration-medium) var(--ease-out-slow),\n      filter var(--duration-medium) var(--ease-out-slow);\n    filter: grayscale(0);\n    scale: var(--zoom-in-ratio);\n  }\n  .section--multicolumn--terpine .enjoy-shift-custom-multicolumn img {\n    filter: grayscale(1);\n    --zoom-in-ratio: 1;\n  }\n  .section--multicolumn--terpine .enjoy-shift-custom-multicolumn {\n    overflow: hidden;\n  }\n}\n/* section--multicolumn--terpine END */\n\n/* section--image-with-text--custom BEGIN */\n.section--image-with-text--custom .image-with-text__text {\n  line-height: 1.25;\n}\n@media screen and (min-width: 750px) {\n  .section--image-with-text--custom .grid--2-col-tablet .grid__item,\n  .section--image-with-text--custom\n    .grid--2-col-tablet\n    .grid__item.grid__item--span-1-tablet {\n    max-width: calc(50% - 4rem);\n  }\n  .section--image-with-text--custom .image-with-text__grid {\n    justify-content: space-between;\n  }\n  .section--image-with-text--custom .image-with-text__content {\n    padding: 4rem 0 5rem;\n  }\n}\n/* section--image-with-text--custom END */\n\n/* section--multicolumn--ig-slider BEGIN */\n.section--multicolumn--ig-slider .multicolumn-card__info,\n.section--multicolumn--ig-slider .slider-counter {\n  display: none;\n}\n.section--multicolumn--ig-slider .slider-button {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #98de59;\n  background: #2b2d1f;\n  border-radius: 50px;\n}\n.section--multicolumn--ig-slider .slider-button[disabled] .icon {\n  color: #98de59;\n  opacity: 0.7;\n}\n.section--multicolumn--ig-slider .slider-button .icon {\n  height: 1rem;\n}\n.section--multicolumn--ig-slider .slider-button--next {\n  right: 2.5rem;\n}\n.section--multicolumn--ig-slider .slider-button--prev {\n  left: 2.5rem;\n}\n\n.section--multicolumn--ig-slider .slider-button:not([disabled]):hover {\n  color: #D8DBC8;\n}\n\n/* section--multicolumn--ig-slider END */\n\n/* three-column-tab-section-custom BEGIN  */\n@media screen and (min-width: 750px) {\n  .three-column-tab-section-custom .accordion__tabs__container {\n    display: flex;\n    align-items: center;\n  }\n  .three-column-tab-section-custom .accordion__tabs {\n    width: 30%;\n    display: flex;\n    flex-direction: column;\n    gap: 2rem !important;\n  }\n  .three-column-tab-section-custom .accordion__tabs__content {\n    width: 70%;\n  }\n  .shopify-section.section.section--collapsible-content .three-column-tab-section-custom .accordion__content {\n    display: flex;\n    flex-wrap: nowrap;\n    flex-direction: row-reverse;\n    align-items: center;\n    margin-bottom: 0;\n    text-align: left;\n  }\n  .three-column-tab-section-custom .collapsible-content__media {\n    clip-path: circle(40%);\n  }\n  .three-column-tab-section-custom .accordion__content_with_image .accordion__content_desktop_left > p {\n    /* set to .caption font settings (above) */\n    font-size: 2rem;\n  }\n  .three-column-tab-section-custom .accordion__content_with_image .accordion__content_desktop_left > p:last-of-type {\n    margin-bottom: 0;\n  }\n  .three-column-tab-section-custom .accordion__content_with_image .accordion__content_desktop_left > p > strong {\n    /* set to .subtitle font settings (above) */\n    font-size: 3.2rem;\n    line-height: 1.2;\n  }\n  .three-column-tab-section-custom .accordion__tabs__container .accordion__tabs-button {\n    padding: 0.5rem 0;\n  }\n  .three-column-tab-section-custom .accordion__tabs-button[active] {\n    border-bottom: none;\n  }\n  .shopify-section.section.section--collapsible-content .three-column-tab-section-custom .accordion__tabs__container .accordion__tabs-button .accordion__tabs-button__title {\n    display: inline;\n    border-bottom: 2px solid transparent;\n  }\n  .shopify-section.section.section--collapsible-content .three-column-tab-section-custom .accordion__tabs__container .accordion__tabs-button {\n    padding-right: 0;\n    line-height: 2;\n  }\n  .shopify-section.section.section--collapsible-content .three-column-tab-section-custom .accordion__tabs__container .accordion__tabs-button[active] .accordion__tabs-button__title {\n    border-bottom-color: inherit;\n  }\n  .shopify-section.section.section--collapsible-content .three-column-tab-section-custom .accordion__tabs__content {\n    padding: 0;\n    border: none;\n  }\n}\n/* three-column-tab-section-custom END  */\n\n.price__badge--subscription {\n  text-transform: uppercase;\n  letter-spacing: .05rem;\n  word-break: normal;\n}\n\n.header__icon.header__icon__text + .header__icon__text {\n  padding-left: 0;\n}\n@media screen and (min-width: 750px) {\n.header__icon.header__icon__text + .header__icon__text {\n  padding-left: 2.6rem;\n}\n}\n\n/* Junip Reviews */\n\nbody .junip-product-review-title {\nmargin-top: 25px;\n}\n\nbody .junip-recommendation-container, \nbody .junip-verified, body .junip-avatar,\nbody .junip-filters,\nbody .junip-powered-by-footer,\nbody .junip-product-review-summary-container  {\ndisplay: none;\n}\n\nbody .junip-review-author {\n    font-family: var(--font-heading-family);\ntext-transform: uppercase;\n}\n\n.accordion .accordion__content {\npadding-left: 0;\n}\n\nbody .accordion {\nborder-top: 0;\nborder-bottom: 0;\n}\n\n.card .card__inner {\nbackground: transparent;\n}\n\n.product .thumbnail[aria-current],\n.product .thumbnail[aria-current]:focus:not(:focus-visible) {\nbox-shadow: none;\nborder: 0;\n}",
    "global_custom_liquid": "<meta name=\"google-site-verification\" content=\"DG7dkFP6Sd7vis8IHAwDkzIhyO7X2rY3W0FUdI5OhbU\" />",
    "type_heading_font_family": "Acumin_Pro_Wide_Black",
    "type_heading_font_style": "normal",
    "type_heading_font_weight": "400",
    "type_body_font_family": "Acumin_Pro",
    "type_body_font_style": "normal",
    "type_body_font_weight": "400",
    "type_body_font_weight_bold": "700",
    "hxl_mobile_css": "font-size: calc(var(--font-heading-scale) * 4.2rem);line-height: 1;",
    "h0_mobile_css": "font-size: calc(var(--font-heading-scale) * 3.6rem);line-height: 1;",
    "h1_mobile_css": "font-size: calc(var(--font-heading-scale) * 3.2rem);line-height: 1.1;",
    "h2_mobile_css": "font-size: calc(var(--font-heading-scale) * 2.4rem);line-height: 1.1;",
    "h3_mobile_css": "font-size: calc(var(--font-heading-scale) * 2rem);line-height: 1.1;",
    "h4_mobile_css": "font-size: calc(var(--font-heading-scale) * 1.6rem);line-height: 1.1;",
    "h5_mobile_css": "font-size: calc(var(--font-heading-scale) * 1.6rem);--font-heading-family: Acumin_Pro_SemiCond_Semibold, sans-serif;",
    "h6_mobile_css": "font-size: calc(var(--font-heading-scale) * 1.6rem);--font-heading-family: Acumin_Pro_Cond_Semibold, sans-serif;",
    "hxl_desktop_css": "font-size: calc(var(--font-heading-scale) * 9.6rem);line-height: 1;",
    "h0_desktop_css": "font-size: calc(var(--font-heading-scale) * 8.2rem);line-height: 1;",
    "h1_desktop_css": "font-size: calc(var(--font-heading-scale) * 6.4rem);line-height: 1.10;",
    "h2_desktop_css": "font-size: calc(var(--font-heading-scale) * 4.8rem);line-height: 1.1;",
    "h3_desktop_css": "font-size: calc(var(--font-heading-scale) * 3.2rem);line-height: 1.1;",
    "h4_desktop_css": "font-size: calc(var(--font-heading-scale) * 2.4rem);line-height: 1.1;",
    "h5_desktop_css": "font-size: calc(var(--font-heading-scale) * 2.4rem);",
    "h6_desktop_css": "font-size: calc(var(--font-heading-scale) * 2.4rem);",
    "custom_body_css_mobile": "letter-spacing: normal;\nline-height: 1.35;",
    "default_body_font_desktop": "1.6rem",
    "custom_body_css_desktop": "letter-spacing: normal;\nline-height: 1.35;",
    "checkout_logo_image": "shopify://shop_images/checkout_logo_4.svg",
    "checkout_logo_position": "center",
    "checkout_logo_size": "large",
    "checkout_body_background_color": "#d7dbc6",
    "checkout_input_background_color_mode": "white",
    "checkout_sidebar_background_color": "#d7dbc6",
    "checkout_accent_color": "#2b2d20",
    "checkout_button_color": "#2b2d20",
    "sections": {
      "main-password-header": {
        "type": "main-password-header",
        "settings": {
          "color_scheme": "scheme-1"
        }
      },
      "main-password-footer": {
        "type": "main-password-footer",
        "settings": {
          "color_scheme": "scheme-5"
        }
      },
      "main-register": {
        "type": "main-register",
        "settings": {
          "padding_top": 36,
          "padding_bottom": 36
        }
      },
      "main-login": {
        "type": "main-login",
        "settings": {
          "padding_top": 36,
          "padding_bottom": 36
        }
      }
    },
    "content_for_index": [],
    "blocks": {
      "3140817623390692025": {
        "type": "shopify://apps/na-age-verification/blocks/agechecker_y1K8NR3dvv/15fad4c9-4ba9-4c27-8e6c-1f214a81924f",
        "disabled": false,
        "settings": {}
      },
      "5460033060862993441": {
        "type": "shopify://apps/essential-announcement-bar/blocks/app-embed/93b5429f-c8d6-4c33-ae14-250fd84f361b",
        "disabled": false,
        "settings": {}
      },
      "16017602894386113438": {
        "type": "shopify://apps/junip-product-reviews-ugc/blocks/junip-store-key/dc14f5a8-ed15-41b1-ad08-cfba23f9789b",
        "disabled": false,
        "settings": {
          "enablePreviewMode": true
        }
      },
      "11567226959186545843": {
        "type": "shopify://apps/savedby-package-protection/blocks/savedby-storefront-widget/6d4bce9c-696c-4f95-8a15-0cc2b2b9e839",
        "disabled": false,
        "settings": {
          "hideSubtotal": true,
          "parentMarginBottom": 4,
          "infoIconUrl": "https://cdn.savedby.io/logos/savedby/SavedByLogo-small.png",
          "infoBGColor": "#4c4e41",
          "infoTextColor": "#95978c",
          "checkoutButtonBGColor": "#98de59",
          "checkoutButtonTextColor": "#2b2d20",
          "checkoutButtonBorderRadius": 24,
          "continueTextColor": "#d7dbc6",
          "continueFontSize": 15,
          "showLockIcon": false,
          "showCartTotal": true,
          "showCompareAtPrice": false,
          "showInlineCartButton": false,
          "swapButtonOrder": false,
          "disclaimerLocation": "bottom",
          "disclaimer": "",
          "customCheckoutSelector": "",
          "tosSelector": "",
          "customCSS": ".sb__button {\n  font-size: 2.2rem;\n  font-family: acumin_pro_cond, sans-serif;\n  font-style: var(--font-heading-style);\n  font-weight: var(--font-heading-weight);\n  letter-spacing: calc(var(--font-heading-scale)* 0.06rem);\n  text-transform: uppercase;\n}\n\n.sb__button:hover {\ntransition: transform var(--duration-default) var(--ease-out-slow);\n}\n\n.sb__info-container {\n  border-radius: 12px;\n}\n\n:host([data-path=\"/cart\"]) .sb__non-covered-link {\n  color: #2b2d20 !important\n}",
          "customJS": "window.addEventListener(\"SavedBy:ready\", () => {\n  if (window.agecheckerVsARddsz9) \n window.SavedBy.checkoutButtonDisabled = true;\n})\nwindow.SavedBy.onCheckout(({ attributes }) => {\n\tconst tos = document.querySelector(\"#agechecker_payment_buttons_checkbox\");\n\n\tif (!tos) return true;\n\tif (tos.checked) {\n\t\tconsole.info(\"[SavedBy] custom JS is setting the Age Verification Status\");\n\t\tattributes[\"Age Verification Status\"] = \"Verified\";\n\t\treturn true;\n\t}\n\talert(\"You must be at least 21 years old to checkout\");\n\treturn false;\n});\n\n\n// WATCH AGE GATE\ndocument.addEventListener(\"change\", (e) => {\n  if (e.target.matches(\"#agechecker_payment_buttons_checkbox\")) {\n  SavedBy.checkoutButtonDisabled = !e.target.checked;\n}\n})\n\nconst styleOverride = document.createElement(\"style\")\nstyleOverride.textContent = `\nbutton[savedby-hidden]:not(savedby-checkout-plus [savedby-hidden]) {\n  position: absolute !important;\n  visibility: hidden;\n  display: block !important;\n}`\ndocument.head.lastChild.after(styleOverride)",
          "popupOverride": ""
        }
      },
      "10312807297210203588": {
        "type": "shopify://apps/aftersell-by-rokt/blocks/utm-triggers/509ec35d-0632-4dfc-a105-fa799cd9d149",
        "disabled": false,
        "settings": {}
      },
      "7280966411886983154": {
        "type": "shopify://apps/monk/blocks/app-embed/e949adb6-87ca-4b97-8f31-0e51bf507f2d",
        "disabled": false,
        "settings": {}
      },
      "13768625480086291342": {
        "type": "shopify://apps/forms/blocks/forms/8744a304-fcb1-4347-b211-bb6b4759a76a",
        "disabled": false,
        "settings": {}
      }
    },
    "color_schemes": {
      "scheme-1": {
        "settings": {
          "background": "#d7dbc6",
          "background_gradient": "",
          "text": "#2b2d20",
          "button": "#2b2d20",
          "button_label": "#98de59",
          "secondary_button_label": "#2b2d20",
          "shadow": "#2b2d20"
        }
      },
      "scheme-2": {
        "settings": {
          "background": "#2b2d20",
          "background_gradient": "",
          "text": "#d8dbc8",
          "button": "#98de59",
          "button_label": "#2b2d20",
          "secondary_button_label": "#d8dbc8",
          "shadow": "#d8dbc8"
        }
      },
      "scheme-3": {
        "settings": {
          "background": "#a9aba1",
          "background_gradient": "linear-gradient(104deg, rgba(169, 171, 161, 1), rgba(208, 210, 200, 1) 97%)",
          "text": "#2b2d1f",
          "button": "#2b2d20",
          "button_label": "#a9aba1",
          "secondary_button_label": "#2b2d1f",
          "shadow": "#2b2d1f"
        }
      },
      "scheme-4": {
        "settings": {
          "background": "#a4ab83",
          "background_gradient": "",
          "text": "#2b2d1f",
          "button": "#2b2d1f",
          "button_label": "#a4ab83",
          "secondary_button_label": "#2b2d1f",
          "shadow": "#2b2d1f"
        }
      },
      "scheme-5": {
        "settings": {
          "background": "#98de59",
          "background_gradient": "",
          "text": "#2b2d20",
          "button": "#98de59",
          "button_label": "#2b2d20",
          "secondary_button_label": "#2b2d20",
          "shadow": "#2b2d20"
        }
      },
      "scheme-6": {
        "settings": {
          "background": "#2b2d20",
          "background_gradient": "",
          "text": "#98de59",
          "button": "#98de59",
          "button_label": "#2b2d20",
          "secondary_button_label": "#98de59",
          "shadow": "#98de59"
        }
      },
      "scheme-7": {
        "settings": {
          "background": "#2b2d20",
          "background_gradient": "",
          "text": "#c52564",
          "button": "#c52564",
          "button_label": "#2b2d20",
          "secondary_button_label": "#c52564",
          "shadow": "#c52564"
        }
      },
      "scheme-8": {
        "settings": {
          "background": "#2b2d20",
          "background_gradient": "",
          "text": "#eda60d",
          "button": "#eda60d",
          "button_label": "#2b2d20",
          "secondary_button_label": "#eda60d",
          "shadow": "#eda60d"
        }
      },
      "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba": {
        "settings": {
          "background": "#f7f7f3",
          "background_gradient": "",
          "text": "#2b2d20",
          "button": "#2b2d20",
          "button_label": "#f7f7f3",
          "secondary_button_label": "#2b2d20",
          "shadow": "#2b2d20"
        }
      }
    }
  },
  "presets": {
    "Default": {
      "logo_width": 90,
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "background": "#FFFFFF",
            "background_gradient": "",
            "text": "#121212",
            "button": "#121212",
            "button_label": "#FFFFFF",
            "secondary_button_label": "#121212",
            "shadow": "#121212"
          }
        },
        "scheme-2": {
          "settings": {
            "background": "#F3F3F3",
            "background_gradient": "",
            "text": "#121212",
            "button": "#121212",
            "button_label": "#F3F3F3",
            "secondary_button_label": "#121212",
            "shadow": "#121212"
          }
        },
        "scheme-3": {
          "settings": {
            "background": "#F3F3F3",
            "background_gradient": "",
            "text": "#121212",
            "button": "#121212",
            "button_label": "#F3F3F3",
            "secondary_button_label": "#121212",
            "shadow": "#121212"
          }
        },
        "scheme-4": {
          "settings": {
            "background": "#F3F3F3",
            "background_gradient": "",
            "text": "#121212",
            "button": "#121212",
            "button_label": "#F3F3F3",
            "secondary_button_label": "#121212",
            "shadow": "#121212"
          }
        },
        "scheme-5": {
          "settings": {
            "background": "#242833",
            "background_gradient": "",
            "text": "#FFFFFF",
            "button": "#FFFFFF",
            "button_label": "#000000",
            "secondary_button_label": "#FFFFFF",
            "shadow": "#121212"
          }
        },
        "scheme-6": {
          "settings": {
            "background": "#121212",
            "background_gradient": "",
            "text": "#FFFFFF",
            "button": "#FFFFFF",
            "button_label": "#121212",
            "secondary_button_label": "#FFFFFF",
            "shadow": "#121212"
          }
        },
        "scheme-7": {
          "settings": {
            "background": "#334FB4",
            "background_gradient": "",
            "text": "#FFFFFF",
            "button": "#FFFFFF",
            "button_label": "#334FB4",
            "secondary_button_label": "#FFFFFF",
            "shadow": "#121212"
          }
        },
        "scheme-8": {
          "settings": {
            "background": "#334FB4",
            "background_gradient": "",
            "text": "#FFFFFF",
            "button": "#FFFFFF",
            "button_label": "#334FB4",
            "secondary_button_label": "#FFFFFF",
            "shadow": "#121212"
          }
        },
        "scheme-9": {
          "settings": {
            "background": "#334FB4",
            "background_gradient": "",
            "text": "#FFFFFF",
            "button": "#FFFFFF",
            "button_label": "#334FB4",
            "secondary_button_label": "#FFFFFF",
            "shadow": "#121212"
          }
        }
      },
      "type_header_font": "assistant_n4",
      "heading_scale": 100,
      "type_body_font": "assistant_n4",
      "body_scale": 100,
      "page_width": 1200,
      "spacing_sections": 0,
      "spacing_grid_horizontal": 8,
      "spacing_grid_vertical": 8,
      "buttons_border_thickness": 1,
      "buttons_border_opacity": 100,
      "buttons_radius": 0,
      "buttons_shadow_opacity": 0,
      "buttons_shadow_horizontal_offset": 0,
      "buttons_shadow_vertical_offset": 4,
      "buttons_shadow_blur": 5,
      "variant_pills_border_thickness": 1,
      "variant_pills_border_opacity": 55,
      "variant_pills_radius": 40,
      "variant_pills_shadow_opacity": 0,
      "variant_pills_shadow_horizontal_offset": 0,
      "variant_pills_shadow_vertical_offset": 4,
      "variant_pills_shadow_blur": 5,
      "inputs_border_thickness": 1,
      "inputs_border_opacity": 55,
      "inputs_radius": 0,
      "inputs_shadow_opacity": 0,
      "inputs_shadow_horizontal_offset": 0,
      "inputs_shadow_vertical_offset": 4,
      "inputs_shadow_blur": 5,
      "card_style": "standard",
      "card_image_padding": 0,
      "card_text_alignment": "left",
      "card_color_scheme": "scheme-2",
      "card_border_thickness": 0,
      "card_border_opacity": 10,
      "card_corner_radius": 0,
      "card_shadow_opacity": 0,
      "card_shadow_horizontal_offset": 0,
      "card_shadow_vertical_offset": 4,
      "card_shadow_blur": 5,
      "collection_card_style": "standard",
      "collection_card_image_padding": 0,
      "collection_card_text_alignment": "left",
      "collection_card_color_scheme": "scheme-2",
      "collection_card_border_thickness": 0,
      "collection_card_border_opacity": 10,
      "collection_card_corner_radius": 0,
      "collection_card_shadow_opacity": 0,
      "collection_card_shadow_horizontal_offset": 0,
      "collection_card_shadow_vertical_offset": 4,
      "collection_card_shadow_blur": 5,
      "blog_card_style": "standard",
      "blog_card_image_padding": 0,
      "blog_card_text_alignment": "left",
      "blog_card_color_scheme": "scheme-2",
      "blog_card_border_thickness": 0,
      "blog_card_border_opacity": 10,
      "blog_card_corner_radius": 0,
      "blog_card_shadow_opacity": 0,
      "blog_card_shadow_horizontal_offset": 0,
      "blog_card_shadow_vertical_offset": 4,
      "blog_card_shadow_blur": 5,
      "text_boxes_border_thickness": 0,
      "text_boxes_border_opacity": 10,
      "text_boxes_radius": 0,
      "text_boxes_shadow_opacity": 0,
      "text_boxes_shadow_horizontal_offset": 0,
      "text_boxes_shadow_vertical_offset": 4,
      "text_boxes_shadow_blur": 5,
      "media_border_thickness": 1,
      "media_border_opacity": 5,
      "media_radius": 0,
      "media_shadow_opacity": 0,
      "media_shadow_horizontal_offset": 0,
      "media_shadow_vertical_offset": 4,
      "media_shadow_blur": 5,
      "popup_border_thickness": 1,
      "popup_border_opacity": 10,
      "popup_corner_radius": 0,
      "popup_shadow_opacity": 5,
      "popup_shadow_horizontal_offset": 0,
      "popup_shadow_vertical_offset": 4,
      "popup_shadow_blur": 5,
      "drawer_border_thickness": 1,
      "drawer_border_opacity": 10,
      "drawer_shadow_opacity": 0,
      "drawer_shadow_horizontal_offset": 0,
      "drawer_shadow_vertical_offset": 4,
      "drawer_shadow_blur": 5,
      "badge_position": "bottom left",
      "badge_corner_radius": 40,
      "sale_badge_color_scheme": "scheme-5",
      "sold_out_badge_color_scheme": "scheme-3",
      "social_twitter_link": "",
      "social_facebook_link": "",
      "social_pinterest_link": "",
      "social_instagram_link": "",
      "social_tiktok_link": "",
      "social_tumblr_link": "",
      "social_snapchat_link": "",
      "social_youtube_link": "",
      "social_vimeo_link": "",
      "predictive_search_enabled": true,
      "predictive_search_show_vendor": false,
      "predictive_search_show_price": false,
      "currency_code_enabled": true,
      "cart_type": "notification",
      "show_vendor": false,
      "show_cart_note": false,
      "cart_drawer_collection": "",
      "sections": {
        "main-password-header": {
          "type": "main-password-header",
          "settings": {
            "color_scheme": "scheme-2"
          }
        },
        "main-password-footer": {
          "type": "main-password-footer",
          "settings": {
            "color_scheme": "scheme-2"
          }
        }
      }
    }
  }
}
