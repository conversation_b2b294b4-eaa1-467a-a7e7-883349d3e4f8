{% comment %}
  bs-add
  - added padding options
{% endcomment %}

{{ 'section-main-page.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

<div class="page-width page-width--narrow section-{{ section.id }}-padding">
  <h1 class="main-page-title page-title {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
    {{ page.title | escape }}
  </h1>
  <div class="rte{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
    {{ page.content }}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.main-page.name",
  "tag": "section",
  "class": "section section--main-page",
  "settings": [
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h0",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "header",
      "content": "Padding options"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 36
    }
  ]
}
{% endschema %}