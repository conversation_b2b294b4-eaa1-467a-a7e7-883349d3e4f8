{% comment %}
  bs-add
  - support for accordion icon images
{% endcomment %}

{%- if image_icon != blank -%}
  {%- assign media_width = 1 -%}
  {%- capture sizes -%}
    (min-width: {{ settings.page_width }}px) calc(({{ settings.page_width | minus: 100 | times: media_width | round }} - 4rem) / 4),
    (min-width: 990px) calc(({{ media_width | times: 100 }}vw - 4rem) / 4),
    (min-width: 750px) calc((100vw - 15rem) / 8),
    calc((100vw - 8rem) / 3)
  {%- endcapture -%}
  {{
    image_icon
    | image_url: width: 416
    | image_tag:
      loading: 'lazy',
      fetchpriority: 'low',
      decoding: 'async',
      class: 'svg-wrapper',
      sizes: sizes,
      widths: '54, 74, 104, 162, 208, 324, 416',
      alt: image_icon.alt
    | escape
  }}
{%- elsif icon != 'none' -%}
  {%- assign file = icon | replace: '_', '-' | prepend: 'icon-' | append: '.svg' -%}
  <span class="svg-wrapper">{{ file | inline_asset_content }}</span>
{%- endif -%}