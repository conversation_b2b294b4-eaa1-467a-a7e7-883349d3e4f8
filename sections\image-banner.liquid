 {% comment %}
  bs-add
  - ability to have video background
  - ability to have image and video specific to mobile/desktop
  - ability to have set content height for desktop/mobile
  - ability to push buttons to bottom for desktop/mobile
  - support for padding top / bottom
  - support for radius
  bs-remove
  - Option for second image
{% endcomment %}

{%- liquid 
  assign has_media = false
  if section.settings.image != blank or section.settings.background_video != blank or section.settings.native_background_video != blank
    assign has_media = true
  endif

  assign has_media_mobile = false
  if section.settings.image_mobile != blank or section.settings.background_video_mobile != blank or section.settings.native_background_video_mobile != blank
    assign has_media_mobile = true
  endif 
-%}
{%- if request.design_mode or has_media -%}

{{ 'section-image-banner.css' | asset_url | stylesheet_tag }}

{% comment %}
  bs-modify
  - The Dawn CSS that deals with the height of the banner has been modified to allow for separate desktop/mobile 'adapt' settings and a custom height slider for both mobile and desktop
{% endcomment %}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}

  {%- if section.settings.image_height_mobile == 'adapt' and section.settings.background_video_mobile == blank and section.settings.native_background_video_mobile == blank -%}
    {%- liquid
      assign image_mobile = section.settings.image_mobile
      if image_mobile == blank
        assign image_mobile = section.settings.image
      endif
    -%}
    @media screen and (max-width: 749px) {
      #Banner-{{ section.id }}::before,
      #Banner-{{ section.id }} .banner__media::before,
      #Banner-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
        padding-bottom: {{ 1 | divided_by: image_mobile.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }
  {% elsif section.settings.image_height_mobile == 'custom' %}
    {% if section.settings.show_text_below %}
      @media screen and (max-width: 749px) {
        #Banner-{{ section.id }} .banner__media {
          min-height: calc({{ section.settings.banner_minheight_mobile }}{{ section.settings.banner_minheight_mobile_units }} - var(--header-height, 0px));
        }
      }
    {% else %}
      @media screen and (max-width: 749px) {
        #Banner-{{ section.id }} .banner__media,
        #Banner-{{ section.id }} .banner__content {
          min-height: calc({{ section.settings.banner_minheight_mobile }}{{ section.settings.banner_minheight_mobile_units }} - var(--header-height, 0px));
        }
      }
    {% endif %}
  {% endif %}

  {%- if section.settings.buttons_bottom_desktop -%}
    @media screen and (min-width: 750px) {
      #Banner-{{ section.id }} .banner__box {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      #Banner-{{ section.id }} .banner__buttons.bottom-desktop {
        margin-top: auto;
        order: 1;
      }
    }
  {% endif %}

  {%- if section.settings.buttons_bottom_mobile and section.settings.show_text_below == false -%}
    @media screen and (max-width: 749px) {
      #Banner-{{ section.id }} .banner__box {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
      #Banner-{{ section.id }} .banner__buttons.bottom-mobile {
        margin-top: auto;
        order: 1;
      }
    }
  {% endif %}

  {% comment %}
    bs-mod
    - allow for swapping mobile content placement 
  {% endcomment %}
  {% if section.settings.show_text_below and section.settings.reverse_text_placement_mobile %}
    @media screen and (max-width: 749px) {
      #Banner-{{ section.id }}.banner:not(.banner--stacked) {
        flex-direction: column-reverse;
      }
    }
  {% endif %}

  {%- if section.settings.image_height_desktop == 'adapt' and section.settings.background_video == blank and section.settings.native_background_video == blank-%}
    @media screen and (min-width: 750px) {
      #Banner-{{ section.id }}::before,
      #Banner-{{ section.id }} .banner__media::before {
        padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;
        content: '';
        display: block;
      }
    }
  {% elsif section.settings.image_height_desktop == 'custom' %}
    @media screen and (min-width: 750px) {
      #Banner-{{ section.id }} .banner__media,
      #Banner-{{ section.id }} .banner__content {
        min-height: calc({{ section.settings.banner_minheight_desktop }}{{ section.settings.banner_minheight_desktop_units }} - var(--header-height, 0px));
      }
    }
  {%- endif -%}

  {% comment %}
    bs-mod
    - allow for mobile overlay opacity
  {% endcomment %}
  @media screen and (max-width: 749px) {
    #Banner-{{ section.id }}::after {
      {% if section.settings.overlay_gradient != blank %}
        background: {{ section.settings.overlay_gradient }};
      {% endif %}
      opacity: {{ section.settings.image_overlay_opacity_mobile | divided_by: 100.0 }};
    }
  }
  @media screen and (min-width: 750px) {
    #Banner-{{ section.id }}::after {
      {% if section.settings.overlay_gradient != blank %}
        background: {{ section.settings.overlay_gradient }};
      {% endif %}
      opacity: {{ section.settings.image_overlay_opacity | divided_by: 100.0 }};
    }
  }

  {% comment %}
    bs-mod
    - allow for max width on content
  {% endcomment %}
  {%- if section.settings.override_content_max_width -%}
    @media screen and (min-width: 750px) {
      #Banner-{{ section.id }} .banner__box {
        max-width: {{ section.settings.content_max_width }}rem;
      }
    }
    @media screen and (min-width: 1400px) {
      #Banner-{{ section.id }} .banner__box {
        max-width: {{ section.settings.content_max_width_desktop }}rem;
      }
    }
  {%- endif -%}
{%- endstyle -%}

{%- liquid
  assign full_width = '100vw'
  assign widths = '375, 550, 750, 1100, 1500, 1780, 2000, 3000, 3840'

  if section.settings.image_behavior == 'ambient'
    assign full_width = '120vw'
    assign widths = '450, 660, 900, 1320, 1800, 2136, 2400, 3600, 7680'
  endif

  assign fetch_priority = 'auto'
  if section.index == 1
    assign fetch_priority = 'high'
  endif
-%}

{% comment %}
  bs-modify
  - The first div has been modified to allow for separate mobile and desktop height and alignement. We've also removed references to a second image. 
  - Added .banner-container for media radius feature
{% endcomment %}
<div class="{{ section.settings.custom_css_class }}">
  <div class="color-{{ section.settings.color_scheme_section }} gradient">
    <div class="section-{{ section.id }}-padding">
      <div class="banner-container {{ section.settings.content_width }}">
        <div
          id="Banner-{{ section.id }}"
          class="banner banner--content-align-{{ section.settings.desktop_content_alignment }} banner--content-align-mobile-{{ section.settings.mobile_content_alignment }} banner-mobile-{{ section.settings.image_height_mobile }} banner-desktop-{{ section.settings.image_height_desktop }}{% if section.settings.show_text_below %} banner--mobile-bottom{%- endif -%}{% if section.settings.show_text_box == false %} banner--desktop-transparent{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}"
        >
  
          {% comment %}
          bs-modify
          - Then we replace Dawn's image object with our desktop and mobile video/image objects
          {% endcomment %}
          {%- if has_media or has_media_mobile -%}
            <div class="banner__media media {% unless section.settings.content_width == 'full-width' %}global-media-settings{% endunless %}{% if section.settings.image == blank %} placeholder{% endif %}{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
              {%- liquid
                assign image_height = section.settings.image.width | divided_by: section.settings.image.aspect_ratio
                assign sizes = full_width
              -%}
  
              {% comment %} Desktop image/video {% endcomment %}
              {%- assign desktop_video_classes = 'banner__media-video' -%}
              {%- if section.settings.background_video_mobile != blank or section.settings.native_background_video_mobile != blank -%}
                {%- assign desktop_video_classes = desktop_video_classes | append: ' small-hide' -%}
              {%- endif -%}

              {%- if section.settings.native_background_video != blank -%}
                {{
                  section.settings.native_background_video
                  | video_tag:
                    image_size: '1600x',
                    loop: true,
                    controls: false,
                    muted: true,
                    autoplay: true,
                    class: desktop_video_classes
                }}
              {%- elsif section.settings.background_video != blank -%}
                <video class="{{ desktop_video_classes }}"
                  {% if section.settings.image != blank %}
                    poster="{{ section.settings.image | image_url: width: 1600 }}"
                  {% endif %}
                  playsinline autoplay muted loop>
                  <source src="{{ section.settings.background_video }}" type="video/mp4">
                </video>
              {%- elsif section.settings.image != blank -%}
                {%- liquid 
                  assign fetch_priority_desktop = fetch_priority
                  if section.settings.image_mobile != blank
                    assign image_class_desktop = ' small-hide'
                    assign fetch_priority_desktop = 'auto'
                  endif 
                -%}
                {{
                  section.settings.image
                  | image_url: width: 3840
                  | image_tag:
                    width: section.settings.image.width,
                    height: image_height,
                    class: image_class_desktop,
                    sizes: sizes,
                    widths: widths,
                    fetchpriority: fetch_priority_desktop
                }}
              {%- endif -%}
  
              {% comment %} Mobile image/video {% endcomment %}
              {%- assign mobile_video_classes = 'banner__media-video medium-hide large-up-hide' -%}

              {%- if section.settings.native_background_video_mobile != blank -%}
                {{
                  section.settings.native_background_video_mobile
                  | video_tag:
                    image_size: '800x',
                    loop: true,
                    controls: false,
                    muted: true,
                    autoplay: true,
                    class: mobile_video_classes
                }}
              {% elsif section.settings.background_video_mobile != blank %}
                <video class="{{ mobile_video_classes }}"
                  {% if section.settings.image != blank %}
                    poster="{{ section.settings.image | image_url: width: 800 }}"
                  {% endif %}
                  playsinline autoplay muted loop>
                  <source src="{{ section.settings.background_video_mobile }}" type="video/mp4">
                </video>
              {%- elsif section.settings.image_mobile != blank -%}
                {%- liquid
                  assign image_class_mobile = ' medium-hide large-up-hide'
                  assign image_height_mobile = section.settings.image_mobile.width | divided_by: section.settings.image_mobile.aspect_ratio
                -%}
                {{
                  section.settings.image_mobile
                  | image_url: width: 3840
                  | image_tag:
                    width: section.settings.image_mobile.width,
                    height: image_height_mobile,
                    class: image_class_mobile,
                    sizes: sizes,
                    widths: widths,
                    fetchpriority: fetch_priority
                }}
              {%- endif -%}
            </div>
          {%- else -%}
            <div class="banner__media media {% unless section.settings.content_width == 'full-width' %}global-media-settings{% endunless %}{% if section.settings.image == blank %} placeholder{% endif %}{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}">
              {{ 'hero-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
            </div>
          {%- endif -%}
  
          {% comment %}
          bs-modify
          - There are a variety of small changes here to allow for custom liquid overrides on blocks.
          {% endcomment %}
          <div class="banner__content banner__content--{{ section.settings.desktop_content_position }} page-width{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
            <div class="banner__box content-container content-container--full-width-mobile color-{{ section.settings.color_scheme }} gradient">
              {%- for block in section.blocks -%}
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {% render block %}
                  {%- when 'custom_liquid' -%}
                    {{ block.settings.custom_liquid }}
                  {%- when 'heading' -%}
                    <h2
                      class="banner__heading inline-richtext {{ block.settings.heading_size }}"
                      {{ block.shopify_attributes }}
                    >
                      <span>{{ block.settings.heading_liquid | default: block.settings.heading }}</span>
                    </h2>
                  {%- when 'text' -%}
                    <div class="banner__text rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>
                      <p>{{ block.settings.text_liquid | default: block.settings.text }}</p>
                    </div>
                  {%- when 'buttons' -%}
                    {%- assign button_label_1_content = block.settings.button_label_1_liquid | default: block.settings.button_label_1 -%}
                    {%- assign button_label_2_content = block.settings.button_label_2_liquid | default: block.settings.button_label_2 -%}
  
                    <div
                      class="banner__buttons{% if button_label_1_content != blank and button_label_2_content != blank %} banner__buttons--multiple{% endif %} {% if section.settings.buttons_bottom_desktop %} bottom-desktop{% endif %} {% if section.settings.buttons_bottom_mobile %} bottom-mobile{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {%- if button_label_1_content != blank -%}
                        <a
                          {% if block.settings.button_link_1 == blank %}
                            role="link" aria-disabled="true"
                          {% else %}
                            href="{{ block.settings.button_link_1 }}"
                          {% endif %}
                          class="{{ block.settings.button_style }}"
                        >
                          {{ button_label_1_content }}
                        </a>
                      {%- endif -%}
  
                      {%- if button_label_2_content != blank -%}
                        <a
                          {% if block.settings.button_link_2 == blank %}
                            role="link" aria-disabled="true"
                          {% else %}
                            href="{{ block.settings.button_link_2 }}"
                          {% endif %}
                          class="{{ block.settings.button_style_2 }}"
                        >
                          {{ button_label_2_content }}
                        </a>
                      {%- endif -%}
                    </div>
                {%- endcase -%}
              {%- endfor -%}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{{ section.settings.custom_liquid }}

{% endif %}

{% schema %}
{
  "name": "t:sections.image-banner.name",
  "tag": "section",
  "class": "section section--image-banner",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Desktop media",
      "info": "Accepts image(s) or video(s) and shows on screens >= 750px"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-banner.settings.image.label"
    },
    {
      "type": "url",
      "id": "background_video",
      "label": "First Background Video url",
      "info": "Optional: Will replace first desktop image"
    },
    {
      "type": "video",
      "id": "native_background_video",
      "label": "First Background Video",
      "info": "Optional: Will replace first desktop image and video url"
    },
    {
      "type": "header",
      "content": "Desktop layout options"
    },
    {
      "type": "select",
      "id": "image_height_desktop",
      "options": [
        {
          "value": "small",
          "label": "t:sections.image-banner.settings.image_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-banner.settings.image_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-banner.settings.image_height.options__4.label"
        },
        {
          "value": "adapt",
          "label": "Adapt to height of media"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "medium",
      "label": "Image height (desktop)",
      "info": "If custom, use min-height image slider below | Adapt can not be used for videos"
    },
    {
      "type": "range",
      "id": "banner_minheight_desktop",
      "min": 20,
      "max": 100,
      "step": 5,
      "label": "Banner min-height (desktop)",
      "info": "Only applies if image height is set to custom",
      "default": 75
    },
    {
      "type": "select",
      "id": "banner_minheight_desktop_units",
      "options": [
        {
          "value": "vh",
          "label": "vh"
        },
        {
          "value": "rem",
          "label": "rem"
        }
      ],
      "default": "vh",
      "label": "Banner min-height units (desktop)",
      "info": "Only applies if image height is set to custom"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "top-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__3.label"
        },
        {
          "value": "middle-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__4.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__5.label"
        },
        {
          "value": "middle-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__6.label"
        },
        {
          "value": "bottom-left",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__7.label"
        },
        {
          "value": "bottom-center",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__8.label"
        },
        {
          "value": "bottom-right",
          "label": "t:sections.image-banner.settings.desktop_content_position.options__9.label"
        }
      ],
      "default": "middle-center",
      "label": "t:sections.image-banner.settings.desktop_content_position.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-banner.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.image-banner.settings.desktop_content_alignment.label"
    },
    {
      "type": "checkbox",
      "id": "show_text_box",
      "default": true,
      "label": "t:sections.image-banner.settings.show_text_box.label"
    },
    {
      "type": "checkbox",
      "id": "buttons_bottom_desktop",
      "default": false,
      "label": "Position buttons to bottom of container (desktop)",
      "info": "Disables vertical content positioning"
    },
    {
      "type": "header",
      "content": "Mobile media",
      "info": "Accepts image(s) or video(s) and shows on screens <= 749px"
    },
    {
      "type": "image_picker",
      "id": "image_mobile",
      "label": "Image (mobile)",
      "info": "Optional: Will replace first desktop image"
    },
    {
      "type": "url",
      "id": "background_video_mobile",
      "label": "Background video url (mobile)",
      "info": "Optional: Will replace first desktop image (mobile image too)"
    },
    {
      "type": "video",
      "id": "native_background_video_mobile",
      "label": "Background video (mobile)",
      "info": "Optional: Will replace first desktop image (mobile image/video-url too)"
    },
    {
      "type": "header",
      "content": "t:sections.all.animation.content"
    },
    {
      "type": "select",
      "id": "image_behavior",
      "options": [
        {
          "value": "none",
          "label": "t:sections.all.animation.image_behavior.options__1.label"
        },
        {
          "value": "ambient",
          "label": "t:sections.all.animation.image_behavior.options__2.label"
        },
        {
          "value": "fixed",
          "label": "t:sections.all.animation.image_behavior.options__3.label"
        },
        {
          "value": "zoom-in",
          "label": "t:sections.all.animation.image_behavior.options__4.label"
        }
      ],
      "default": "none",
      "label": "t:sections.all.animation.image_behavior.label"
    },
    {
      "type": "header",
      "content": "Mobile layout options"
    },
    {
      "type": "select",
      "id": "image_height_mobile",
      "options": [
        {
          "value": "small",
          "label": "t:sections.image-banner.settings.image_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-banner.settings.image_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-banner.settings.image_height.options__4.label"
        },
        {
          "value": "adapt",
          "label": "Adapt to height of media"
        },
        {
          "value": "custom",
          "label": "Custom"
        }
      ],
      "default": "medium",
      "label": "Image height (mobile)",
      "info": "If custom, use min-height image slider below | Adapt can not be used for videos"
    },
    {
      "type": "range",
      "id": "banner_minheight_mobile",
      "min": 20,
      "max": 100,
      "step": 5,
      "label": "Banner min-height (mobile)",
      "info": "Only applies if image height is set to custom",
      "default": 75
    },
    {
      "type": "select",
      "id": "banner_minheight_mobile_units",
      "options": [
        {
          "value": "vh",
          "label": "vh"
        },
        {
          "value": "rem",
          "label": "rem"
        }
      ],
      "default": "vh",
      "label": "Banner min-height units (mobile)",
      "info": "Only applies if image height is set to custom"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-banner.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.image-banner.settings.mobile_content_alignment.label"
    },
    {
      "type": "checkbox",
      "id": "show_text_below",
      "default": true,
      "label": "t:sections.image-banner.settings.show_text_below.label",
      "info": "Positions banner content below media"
    }, 
    {
      "type": "checkbox",
      "id": "reverse_text_placement_mobile",
      "default": false,
      "label": "Reverses banner content to above media",
      "info": "'Show container on mobile' needs to be checked (above setting)"
    }, 
    {
      "type": "checkbox",
      "id": "buttons_bottom_mobile",
      "default": false,
      "label": "Position buttons to bottom of container",
      "info": "Does not apply if 'Show container on mobile' is checked"
    },
    {
      "type": "header",
      "content": "Misc options"
    },
    {
      "type": "color_background",
      "id": "overlay_gradient",
      "label": "Custom overlay gradient",
      "info": "Opacity of overlay is controlled from opacity settings below"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity_mobile",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "%",
      "label": "Image overlay opacity (mobile)",
      "default": 0
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "%",
      "label": "t:sections.image-banner.settings.image_overlay_opacity.label",
      "default": 0
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Container color scheme",
      "info": "Applies if container is selected on desktop or mobile",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Advanced: Layout"
    },
    {
      "type": "select",
      "id": "content_width",
      "options": [
        {
          "value": "full-width",
          "label": "Full width on all devices"
        },
        {
          "value": "page-width",
          "label": "Page width (based on theme settings)"
        },
        {
          "value": "page-width page-width--narrow",
          "label": "Page width narrow (tablet and desktop only)"
        }
      ],
      "default": "full-width",
      "label": "Section width"
    },
    {
      "type": "checkbox",
      "id": "override_content_max_width",
      "default": false,
      "label": "Override content max width defaults"
    },
    {
      "type": "range",
      "id": "content_max_width",
      "min": 45,
      "max": 100,
      "step": 1,
      "unit": "rem",
      "label": "Content max width (medium)",
      "info": "Screen between 750px and 1400px",
      "default": 71
    },
    {
      "type": "range",
      "id": "content_max_width_desktop",
      "min": 45,
      "max": 100,
      "step": 1,
      "unit": "rem",
      "label": "Content max width (large)",
      "info": "Screen 1400px and higher",
      "default": 90
    },
    {
      "type": "header",
      "content": "Padding options"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced: Section Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_section",
      "label": "Background color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "page",
      "id": "page",
      "label": "Linked Page",
      "info": "Allows page linking to access metafields in unique cases (e.g. home page)."
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "heading",
      "name": "t:sections.image-banner.blocks.heading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.image-banner.blocks.heading.settings.heading.default",
          "label": "t:sections.image-banner.blocks.heading.settings.heading.label"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-banner.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "t:sections.image-banner.blocks.text.settings.text.default",
          "label": "t:sections.image-banner.blocks.text.settings.text.label"
        },
        {
          "type": "liquid",
          "id": "text_liquid",
          "label": "Description liquid",
          "info": "Overrides Description above"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.image-banner.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-banner.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "buttons",
      "name": "t:sections.image-banner.blocks.buttons.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "button_label_1",
          "default": "t:sections.image-banner.blocks.buttons.settings.button_label_1.default",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_1.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_1.info"
        },
        {
          "type": "liquid",
          "id": "button_label_1_liquid",
          "label": "First button label liquid",
          "info": "Overrides First button label above"
        },
        {
          "type": "url",
          "id": "button_link_1",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_1.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "First button style"
        },
        {
          "type": "inline_richtext",
          "id": "button_label_2",
          "default": "t:sections.image-banner.blocks.buttons.settings.button_label_2.default",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_2.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_2.info"
        },
        {
          "type": "liquid",
          "id": "button_label_2_liquid",
          "label": "Second button label liquid",
          "info": "Overrides Second button label above"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_2.label"
        },
        {
          "type": "select",
          "id": "button_style_2",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "Second button style"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image-banner.presets.name"
    }
  ]
}
{% endschema %}
