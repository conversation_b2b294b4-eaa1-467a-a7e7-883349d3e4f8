/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "settings": {
        "heading_size": "h0",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 56,
        "padding_bottom_desktop": 56
      }
    },
    "d8666e20-a71f-40e1-b978-644d8ec4f4b7": {
      "type": "rich-text",
      "blocks": {
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "Find us",
            "heading_size": "h1",
            "text_indent": ""
          }
        },
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1": {
          "type": "text",
          "settings": {
            "text": "<p>All products are subject to availability. Please contact your retailer to ensure products are in stock. Downshift is typically sold with refrigerated beverages.</p>",
            "text_indent": ""
          }
        },
        "button_JtYDGp": {
          "type": "button",
          "settings": {
            "button_label": "Request Locally",
            "button_link": "#requestlocally",
            "button_style": "button button--primary",
            "button_label_2": "",
            "button_link_2": "",
            "button_style_2": "button button--primary",
            "button_label_liquid": "",
            "button_label_2_liquid": ""
          }
        }
      },
      "block_order": [
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1",
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1",
        "button_JtYDGp"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "left",
        "color_scheme": "scheme-1",
        "padding_top_mobile": 55,
        "padding_top": 40,
        "padding_bottom_mobile": 45,
        "padding_bottom": 0,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "center",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": "<style>\n@media screen and (min-width: 750px) {\n#shopify-section-{{ section.id }} .rich-text__blocks {\n  display: grid;\n  grid-template-columns: 40% auto;\n  align-items: top;\n  justify-items: flex-start;\n}\n#shopify-section-{{ section.id }} .rich-text__blocks .rich-text__text {\n  margin-top: .5rem;\n}}\n</style>"
      }
    },
    "72e84780-8f4c-429b-a8e7-442b2137b76d": {
      "type": "custom-liquid",
      "settings": {
        "description": "",
        "custom_liquid": "<style>\n@media screen and (max-width: 768px) {\n\n#stockist-widget .stockist-horizontal {\n  max-height: 150vh;\n  max-width: 90vw;\n  overflow:scroll;\n}\n\n}\n</style>\n<div class=\"page-width\">\n<div data-stockist-widget-tag=\"u20018\">Loading store locator\nfrom <a href=\"https://stockist.co\">Stockist store locator</a>...</div>\n<script>\n  (function(s,t,o,c,k){c=s.createElement(t);c.src=o;c.async=1;\n  k=s.getElementsByTagName(t)[0];k.parentNode.insertBefore(c,k);\n  })(document,'script','//stockist.co/embed/v1/widget.min.js');\n</script>\n<!-- End Stockist.co widget -->\n</div>",
        "hide_size": "",
        "color_scheme": "scheme-1",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 55,
        "padding_top_desktop": 55,
        "padding_bottom_desktop": 55
      }
    },
    "custom_liquid_kC3UXW": {
      "type": "custom-liquid",
      "name": "t:sections.custom-liquid.presets.name",
      "settings": {
        "description": "",
        "custom_liquid": "<div id=\"requestlocally\"> </div>",
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0
      }
    },
    "17543408734631b3e7": {
      "type": "apps",
      "blocks": {
        "forms_inline_xe7hi6": {
          "type": "shopify://apps/forms/blocks/inline/8744a304-fcb1-4347-b211-bb6b4759a76a",
          "settings": {
            "form_id": "592824",
            "text_color": "#202020",
            "button_background_color": "#202020",
            "button_label_color": "#ffffff",
            "links_color": "#1878b9",
            "errors_color": "#e02229",
            "text_alignment": "center",
            "form_alignment": "center",
            "padding_top": 0,
            "padding_bottom": 25,
            "padding_right": 0,
            "padding_left": 0
          }
        }
      },
      "block_order": [
        "forms_inline_xe7hi6"
      ],
      "settings": {
        "include_margins": true,
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    }
  },
  "order": [
    "main",
    "d8666e20-a71f-40e1-b978-644d8ec4f4b7",
    "72e84780-8f4c-429b-a8e7-442b2137b76d",
    "custom_liquid_kC3UXW",
    "17543408734631b3e7"
  ]
}
