{%- liquid
  assign block_index = 0
  for section_block in section.blocks
    assign block_index = block_index | plus: 1
    if block.id == section_block.id
      break
    endif
  endfor
-%}

<li
  id="Slide-{{ section.id }}-{{ block_index }}"
  class="
    grid__item content-container longshore-custom-list__item tw:flex tw:flex-col
    {% if block.settings.column_gap_mobile %}tw:max-md:gap-y-(--grid-mobile-vertical-spacing){% endif %}
    {% if block.settings.column_gap_desktop %}tw:md:gap-y-(--grid-desktop-vertical-spacing){% endif %}
    {% if block.settings.custom_color_scheme %}color-{{ block.settings.color_scheme }}{% endif %}
    {% if section.settings.swipe_on_mobile %}grid__item--span-1{% else %}{{ block.settings.column_span_amount }}{% endif %}
    {% if section.settings.swipe_on_desktop %}grid__item--span-1-tablet{% else %}{{ block.settings.column_span_amount_desktop }}{% endif %}
    {% if section.settings.swipe_on_mobile or section.settings.swipe_on_desktop %} slider__slide{% endif %}
    {% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}
    {{ block.settings.column_height_mobile }}
    {{ block.settings.column_height_desktop }}
    {{ block.settings.custom_classes }}
  "
  {{ block.shopify_attributes }}
  {% if settings.animations_reveal_on_scroll %}
    data-cascade
    style="--animation-order: {{ block_index }};"
  {% endif %}
>
  {% content_for 'blocks' %}
</li>

{% schema %}
{
  "name": "Column",
  "tag": null,
  "blocks": [{ "type": "@theme" }, { "type": "@app" }],
  "settings": [
    {
      "type": "header",
      "content": "Color"
    },
    {
      "type": "checkbox",
      "id": "custom_color_scheme",
      "label": "Enable block color scheme",
      "info": "If unchecked, column will inherit color scheme",
      "default": false
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Mobile layout",
      "info": "Devices with screens < 750px"
    },
    {
      "type": "select",
      "id": "column_span_amount",
      "options": [
        {
          "value": "grid__item--span-1",
          "label": "1"
        },
        {
          "value": "grid__item--span-2",
          "label": "2"
        }
      ],
      "default": "grid__item--span-1",
      "label": "Column span",
      "info": "Has no effect when using sliders"
    },
    {
      "type": "select",
      "id": "column_height_mobile",
      "options": [
        {
          "value": "tw:max-sm:self-stretch",
          "label": "Stretch"
        },
        {
          "value": "tw:max-sm:self-start tw:max-sm:aspect-square",
          "label": "Square"
        },
        {
          "value": "tw:max-sm:self-start tw:max-sm:aspect-[4/5]",
          "label": "Portrait (4/5)"
        },
        {
          "value": "tw:max-sm:self-start tw:max-sm:aspect-[16/9]",
          "label": "16/9"
        },
        {
          "value": "tw:max-sm:self-start tw:max-sm:aspect-[9/16]",
          "label": "9/16"
        }
      ],
      "default": "tw:max-sm:self-stretch",
      "label": "Column height"
    },
    {
      "type": "checkbox",
      "id": "column_gap_mobile",
      "default": true,
      "label": "Use global grid spacing",
    },
    {
      "type": "header",
      "content": "Desktop layout",
      "info": "Devices with screens >= 750px"
    },
    {
      "type": "select",
      "id": "column_span_amount_desktop",
      "options": [
        {
          "value": "grid__item--span-1-tablet",
          "label": "1"
        },
        {
          "value": "grid__item--span-2-tablet",
          "label": "2"
        },
        {
          "value": "grid__item--span-3-tablet",
          "label": "3"
        },
        {
          "value": "grid__item--span-4-tablet",
          "label": "4"
        }
      ],
      "default": "grid__item--span-1-tablet",
      "label": "Column span",
      "info": "Has no effect when using sliders"
    },
    {
      "type": "select",
      "id": "column_height_desktop",
      "options": [
        {
          "value": "tw:md:self-stretch",
          "label": "Stretch"
        },
        {
          "value": "tw:md:self-start tw:md:aspect-square",
          "label": "Square"
        },
        {
          "value": "tw:md:self-start tw:md:aspect-4/5",
          "label": "Portrait (4/5)"
        },
        {
          "value": "tw:md:self-start tw:md:aspect-16/9",
          "label": "16/9"
        },
        {
          "value": "tw:md:self-start tw:md:aspect-9/16",
          "label": "9/16"
        }
      ],
      "default": "tw:md:self-stretch",
      "label": "Column height"
    },
    {
      "type": "checkbox",
      "id": "column_gap_desktop",
      "default": true,
      "label": "Use global grid spacing",
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "text",
      "id": "custom_classes",
      "label": "Block custom classes"
    }
  ],
  "presets": [
    {
      "name": "Column"
    }
  ]
}
{% endschema %}
