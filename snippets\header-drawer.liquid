{% comment %}
  Renders a header drawer menu for mobile and desktop.

  Usage:
  {% render 'header-drawer' %}
{% endcomment %}

<header-drawer data-breakpoint="{% if section.settings.menu_type_desktop == 'drawer' %}desktop{% else %}tablet{% endif %}">
  <details id="Details-menu-drawer-container" class="menu-drawer-container">
    <summary
      class="header__icon header__icon--menu header__icon--summary link focus-inset"
      aria-label="{{ 'sections.header.menu' | t }}"
    >
      <span>
        {{- 'icon-hamburger.svg' | inline_asset_content -}}
        {{- 'icon-close.svg' | inline_asset_content -}}
      </span>
    </summary>
    <div id="menu-drawer" class="gradient menu-drawer motion-reduce color-{{ section.settings.menu_color_scheme }}">
      <div class="menu-drawer__inner-container">
        <div class="menu-drawer__navigation-container">
          <nav class="menu-drawer__navigation">
            <ul class="menu-drawer__menu has-submenu list-menu" role="list">
              {%- for link in drawer_menu.links -%}
                <li>
                  {% if section.settings.show_content_in_drawer_menu %}
                    {%- capture menu_blocks -%}
                      {% for block in section.blocks %}
                        {%- if link.title == block.settings.title_match %}
                          <li class="menu-drawer__menu-item">
                            {%- case block.type -%}
                              {%- when 'custom_liquid' -%}
                                {{ block.settings.custom_liquid }}
                              {%- when 'image' -%}
                                {% render 'card-image-link',
                                  link_url: block.settings.link,
                                  link_label: block.settings.link_label,
                                  media_image: block.settings.image,
                                  media_aspect_ratio: section.settings.image_ratio,
                                  columns: 2,
                                  extend_height: true,
                                  wrapper_class: 'product-card-wrapper'
                                %}
                              {%- when 'product' -%}
                                {% render 'card-product',
                                  card_product: block.settings.product,
                                  media_aspect_ratio: section.settings.image_ratio,
                                  show_secondary_image: block.settings.second_image,
                                  extend_height: true,
                                  show_alternative_title: block.settings.show_alternative_title
                                %}
                              {%- when 'collection' -%}
                                {% render 'card-collection',
                                  card_collection: block.settings.collection,
                                  media_aspect_ratio: section.settings.image_ratio,
                                  columns: 2,
                                  extend_height: true,
                                  wrapper_class: 'product-card-wrapper'
                                %}
                            {%- endcase -%}
                          </li>
                        {%- endif -%}
                      {% endfor %}
                    {%- endcapture -%}
                  {%- endif -%}

                  {%- if link.links != blank or menu_blocks != blank -%}
                    <details id="Details-menu-drawer-menu-item-{{ forloop.index }}">
                      <summary
                        id="HeaderDrawer-{{ link.handle }}"
                        class="menu-drawer__menu-item list-menu__item link link--text focus-inset{% if link.child_active %} menu-drawer__menu-item--active{% endif %}"
                      >
                        {{ link.title | escape }}
                        <span class="svg-wrapper">
                          {{- 'icon-arrow.svg' | inline_asset_content -}}
                        </span>
                        <span class="svg-wrapper">
                          {{- 'icon-caret.svg' | inline_asset_content -}}
                        </span>
                      </summary>
                      <div
                        id="link-{{ link.handle | escape }}"
                        class="menu-drawer__submenu has-submenu gradient motion-reduce"
                        tabindex="-1"
                      >
                        <div class="menu-drawer__inner-submenu">
                          <button class="menu-drawer__close-button link link--text focus-inset" aria-expanded="true">
                            <span class="svg-wrapper">
                              {{- 'icon-arrow.svg' | inline_asset_content -}}
                            </span>
                            {{ link.title | escape }}
                          </button>
                          <ul class="menu-drawer__menu list-menu" role="list" tabindex="-1">
                            {%- for childlink in link.links -%}
                              <li>
                                {%- if childlink.links == blank -%}
                                  <a
                                    id="HeaderDrawer-{{ link.handle }}-{{ childlink.handle }}"
                                    href="{{ childlink.url }}"
                                    class="menu-drawer__menu-item link link--text list-menu__item focus-inset{% if childlink.current %} menu-drawer__menu-item--active{% endif %}"
                                    {% if childlink.current %}
                                      aria-current="page"
                                    {% endif %}
                                  >
                                    {{ childlink.title | escape }}
                                  </a>
                                {%- else -%}
                                  <details id="Details-menu-drawer-{{ link.handle }}-{{ childlink.handle }}">
                                    <summary
                                      id="HeaderDrawer-{{ link.handle }}-{{ childlink.handle }}"
                                      class="menu-drawer__menu-item link link--text list-menu__item focus-inset"
                                    >
                                      {{ childlink.title | escape }}
                                      <span class="svg-wrapper">
                                        {{- 'icon-arrow.svg' | inline_asset_content -}}
                                      </span>
                                      <span class="svg-wrapper">
                                        {{- 'icon-caret.svg' | inline_asset_content -}}
                                      </span>
                                    </summary>
                                    <div
                                      id="childlink-{{ childlink.handle | escape }}"
                                      class="menu-drawer__submenu has-submenu gradient motion-reduce"
                                    >
                                      <button
                                        class="menu-drawer__close-button link link--text focus-inset"
                                        aria-expanded="true"
                                      >
                                        <span class="svg-wrapper">
                                          {{- 'icon-arrow.svg' | inline_asset_content -}}
                                        </span>
                                        {{ childlink.title | escape }}
                                      </button>
                                      <ul
                                        class="menu-drawer__menu list-menu"
                                        role="list"
                                        tabindex="-1"
                                      >
                                        {%- for grandchildlink in childlink.links -%}
                                          <li>
                                            <a
                                              id="HeaderDrawer-{{ link.handle }}-{{ childlink.handle }}-{{ grandchildlink.handle }}"
                                              href="{{ grandchildlink.url }}"
                                              class="menu-drawer__menu-item link link--text list-menu__item focus-inset{% if grandchildlink.current %} menu-drawer__menu-item--active{% endif %}"
                                              {% if grandchildlink.current %}
                                                aria-current="page"
                                              {% endif %}
                                            >
                                              {{ grandchildlink.title | escape }}
                                            </a>
                                          </li>
                                        {%- endfor -%}
                                      </ul>
                                    </div>
                                  </details>
                                {%- endif -%}
                              </li>
                            {%- endfor -%}

                            {{ menu_blocks }}

                          </ul>
                        </div>
                      </div>
                    </details>
                  {%- else -%}
                    <a
                      id="HeaderDrawer-{{ link.handle }}"
                      href="{{ link.url }}"
                      class="menu-drawer__menu-item list-menu__item link link--text focus-inset{% if link.current %} menu-drawer__menu-item--active{% endif %}"
                      {% if link.current %}
                        aria-current="page"
                      {% endif %}
                    >
                      {{ link.title | escape }}
                    </a>
                  {%- endif -%}
                </li>
              {%- endfor -%}
            </ul>

            {%- if section.settings.drawer_custom_liquid != blank -%}
              {{ section.settings.drawer_custom_liquid }}
            {%- endif -%}
          </nav>
          <div class="menu-drawer__utility-links">
            {%- if shop.customer_accounts_enabled -%}
              <a
                href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}"
                class="menu-drawer__account link focus-inset h5 medium-hide large-up-hide"
                rel="nofollow"
              >
                {%- if section.settings.enable_customer_avatar -%}
                  <account-icon>
                    {%- if customer and customer.has_avatar? -%}
                      {{ customer | avatar }}
                    {%- else -%}
                      <span class="svg-wrapper">
                        {{- 'icon-account.svg' | inline_asset_content -}}
                      </span>
                    {%- endif -%}
                  </account-icon>
                {%- else -%}
                  <span class="svg-wrapper">
                    {{- 'icon-account.svg' | inline_asset_content -}}
                  </span>
                {%- endif -%}
                {%- liquid
                  if customer
                    echo 'customer.account_fallback' | t
                  else
                    echo 'customer.log_in' | t
                  endif
                -%}
              </a>
            {%- endif -%}
            {%- if localization.available_countries or localization.available_languages -%}
              <div class="menu-drawer__localization header-localization">
                {%- if localization.available_countries and localization.available_countries.size > 1 -%}
                  <localization-form>
                    {%- form 'localization', id: 'HeaderCountryMobileForm', class: 'localization-form' -%}
                      <div>
                        <p class="visually-hidden" id="HeaderCountryMobileLabel">
                          {{ 'localization.country_label' | t }}
                        </p>
                        {%- render 'country-localization', localPosition: 'HeaderCountryMobile' -%}
                      </div>
                    {%- endform -%}
                  </localization-form>
                {% endif %}

                {%- if localization.available_languages and localization.available_languages.size > 1 -%}
                  <localization-form>
                    {%- form 'localization', id: 'HeaderLanguageMobileForm', class: 'localization-form' -%}
                      <div>
                        <p class="visually-hidden" id="HeaderLanguageMobileLabel">
                          {{ 'localization.language_label' | t }}
                        </p>
                        {%- render 'language-localization', localPosition: 'HeaderLanguageMobile' -%}
                      </div>
                    {%- endform -%}
                  </localization-form>
                {%- endif -%}
              </div>
            {%- endif -%}
            <ul class="list list-social list-unstyled" role="list">
              {%- if settings.social_twitter_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_twitter_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-twitter.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.twitter' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_facebook_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_facebook_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-facebook.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.facebook' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_pinterest_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_pinterest_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-pinterest.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.pinterest' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_instagram_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_instagram_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-instagram.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.instagram' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_tiktok_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_tiktok_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-tiktok.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.tiktok' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_tumblr_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_tumblr_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-tumblr.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.tumblr' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_snapchat_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_snapchat_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-snapchat.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.snapchat' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_youtube_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_youtube_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-youtube.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.youtube' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
              {%- if settings.social_vimeo_link != blank -%}
                <li class="list-social__item">
                  <a href="{{ settings.social_vimeo_link }}" class="list-social__link link">
                    <span class="svg-wrapper">
                      {{- 'icon-vimeo.svg' | inline_asset_content -}}
                    </span>
                    <span class="visually-hidden">{{ 'general.social.links.vimeo' | t }}</span>
                  </a>
                </li>
              {%- endif -%}
            </ul>
          </div>
        </div>
      </div>
    </div>
  </details>
</header-drawer>