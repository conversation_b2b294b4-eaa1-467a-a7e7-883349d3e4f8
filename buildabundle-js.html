<script>
    // handle dynamic Add to Cart functionality
    document.addEventListener('DOMContentLoaded', () => {
        const bundleATCbtns = document.querySelectorAll('.buildabundle-addtocart .button');  
        bundleATCbtns.forEach(thisButton => {
            thisButton.addEventListener('click', function(event) {
                event.preventDefault();
                // find child <script> tag with ID starting with 'buildabundle-addtocart'
                const bundleContents = thisButton.querySelector('script[id^="buildabundle-addtocart"]');
                if(bundleContents){
                    const bundleJSON = JSON.parse(bundleContents.textContent);

                    // add bundle to cart
                    fetch(window.Shopify.routes.root + 'cart/add.js', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(bundleJSON)
                    })
                    .then(response => {
                        window.location.href = '/cart'
                        return response.json();
                    })
                    .catch((error) => {
                        console.error('Error:', error);
                    });
                }
            });
        });
    });
</script>