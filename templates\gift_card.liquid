{% layout none %}

<!doctype html>
<html lang="{{ request.locale.iso_code }}">
  <head>
    <script src="{{ 'vendor/qrcode.js' | shopify_asset_url }}" defer></script>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="{{ settings.color_background }}">
    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    {%- assign formatted_balance = gift_card.balance | money_without_trailing_zeros | strip_html -%}

    <title>{{ 'gift_cards.issued.title' | t: value: formatted_balance, shop: shop.name }}</title>

    <meta name="description" content="{{ 'gift_cards.issued.subtext' | t }}">

    {{ content_for_header }}

    {%- liquid
      assign body_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
      assign body_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
      assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
    %}


    {% style %}
        {{ settings.type_body_font | font_face: font_display: 'swap' }}
        {{ body_font_bold | font_face: font_display: 'swap' }}
        {{ body_font_italic | font_face: font_display: 'swap' }}
        {{ body_font_bold_italic | font_face: font_display: 'swap' }}
        {{ settings.type_header_font | font_face: font_display: 'swap' }}

      {% comment %}
        bs-add
        - remove opacity for text color
      {% endcomment %}
      {% for scheme in settings.color_schemes -%}
        {% assign scheme_classes = scheme_classes | append: ', .color-' | append: scheme.id %}
        {% if forloop.index == 1 -%}
          :root,
        {%- endif %}
        .color-{{ scheme.id }} {
          --color-background: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
          {% if scheme.settings.background_gradient != empty %}
            --gradient-background: {{ scheme.settings.background_gradient }};
          {% else %}
            --gradient-background: {{ scheme.settings.background }};
          {% endif %}

          {% liquid
            assign background_color = scheme.settings.background
            assign background_color_brightness = background_color | color_brightness
            if background_color_brightness <= 26
              assign background_color_contrast = background_color | color_lighten: 50
            elsif background_color_brightness <= 65
              assign background_color_contrast = background_color | color_lighten: 5
            else
              assign background_color_contrast = background_color | color_darken: 25
            endif
          %}

          --color-foreground: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
          --color-background-contrast: {{ background_color_contrast.red }},{{ background_color_contrast.green }},{{ background_color_contrast.blue }};
          --color-shadow: {{ scheme.settings.shadow.red }},{{ scheme.settings.shadow.green }},{{ scheme.settings.shadow.blue }};
          --color-button: {{ scheme.settings.button.red }},{{ scheme.settings.button.green }},{{ scheme.settings.button.blue }};
          --color-button-text: {{ scheme.settings.button_label.red }},{{ scheme.settings.button_label.green }},{{ scheme.settings.button_label.blue }};
          --color-secondary-button: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
          --color-secondary-button-text: {{ scheme.settings.secondary_button_label.red }},{{ scheme.settings.secondary_button_label.green }},{{ scheme.settings.secondary_button_label.blue }};
          --color-link: {{ scheme.settings.secondary_button_label.red }},{{ scheme.settings.secondary_button_label.green }},{{ scheme.settings.secondary_button_label.blue }};
          --color-badge-foreground: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
          --color-badge-background: {{ scheme.settings.background.red }},{{ scheme.settings.background.green }},{{ scheme.settings.background.blue }};
          --color-badge-border: {{ scheme.settings.text.red }},{{ scheme.settings.text.green }},{{ scheme.settings.text.blue }};
          --payment-terms-background-color: rgb({{ scheme.settings.background.rgb }});
        }
      {% endfor %}

      {{ scheme_classes | prepend: 'body' }} {
        color: rgba(var(--color-foreground), 1);
        background-color: rgb(var(--color-background));
      }

      :root {
        --font-body-family: {{ settings.type_body_font_family | default: settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
        --font-body-style: {{ settings.type_body_font_style | default: settings.type_body_font.style }};
        --font-body-weight: {{ settings.type_body_font_weight | default: settings.type_body_font.weight }};
        --font-body-weight-bold: {{ settings.type_body_font_weight_bold | default: settings.type_body_font.weight | plus: 300 | at_most: 1000 }};

        --font-heading-family: {{ settings.type_heading_font_family | default: settings.type_header_font.family }}, {{ settings.type_header_font.fallback_families }};
        --font-heading-style: {{ settings.type_heading_font_style | default: settings.type_header_font.style }};
        --font-heading-weight: {{ settings.type_heading_font_weight | default: settings.type_header_font.weight }};

        --font-body-scale: {{ settings.body_scale | divided_by: 100.0 }};
        --font-heading-scale: {{ settings.heading_scale | times: 1.0 | divided_by: settings.body_scale }};

        --media-padding: {{ settings.media_padding }}px;
        --media-border-opacity: {{ settings.media_border_opacity | divided_by: 100.0 }};
        --media-border-width: {{ settings.media_border_thickness }}px;
        --media-radius: {{ settings.media_radius }}px;
        --media-shadow-opacity: {{ settings.media_shadow_opacity | divided_by: 100.0 }};
        --media-shadow-horizontal-offset: {{ settings.media_shadow_horizontal_offset }}px;
        --media-shadow-vertical-offset: {{ settings.media_shadow_vertical_offset }}px;
        --media-shadow-blur-radius: {{ settings.media_shadow_blur }}px;
        --media-shadow-visible: {% if settings.media_shadow_opacity > 0 %}1{% else %}0{% endif %};

        --page-width: {{ settings.page_width | divided_by: 10 }}rem;
        --page-width-margin: {% if settings.page_width == '1600' %}2{% else %}0{% endif %}rem;

        --product-card-image-padding: {{ settings.card_image_padding | divided_by: 10.0 }}rem;
        --product-card-corner-radius: {{ settings.card_corner_radius | divided_by: 10.0 }}rem;
        --product-card-text-alignment: {{ settings.card_text_alignment }};
        --product-card-border-width: {{ settings.card_border_thickness | divided_by: 10.0 }}rem;
        --product-card-border-opacity: {{ settings.card_border_opacity | divided_by: 100.0 }};
        --product-card-shadow-opacity: {{ settings.card_shadow_opacity | divided_by: 100.0 }};
        --product-card-shadow-visible: {% if settings.card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --product-card-shadow-horizontal-offset: {{ settings.card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --product-card-shadow-vertical-offset: {{ settings.card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --product-card-shadow-blur-radius: {{ settings.card_shadow_blur | divided_by: 10.0 }}rem;

        --collection-card-image-padding: {{ settings.collection_card_image_padding | divided_by: 10.0 }}rem;
        --collection-card-corner-radius: {{ settings.collection_card_corner_radius | divided_by: 10.0 }}rem;
        --collection-card-text-alignment: {{ settings.collection_card_text_alignment }};
        --collection-card-border-width: {{ settings.collection_card_border_thickness | divided_by: 10.0 }}rem;
        --collection-card-border-opacity: {{ settings.collection_card_border_opacity | divided_by: 100.0 }};
        --collection-card-shadow-opacity: {{ settings.collection_card_shadow_opacity | divided_by: 100.0 }};
        --collection-card-shadow-visible: {% if settings.collection_card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --collection-card-shadow-horizontal-offset: {{ settings.collection_card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --collection-card-shadow-vertical-offset: {{ settings.collection_card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --collection-card-shadow-blur-radius: {{ settings.collection_card_shadow_blur | divided_by: 10.0 }}rem;

        --blog-card-image-padding: {{ settings.blog_card_image_padding | divided_by: 10.0 }}rem;
        --blog-card-corner-radius: {{ settings.blog_card_corner_radius | divided_by: 10.0 }}rem;
        --blog-card-text-alignment: {{ settings.blog_card_text_alignment }};
        --blog-card-border-width: {{ settings.blog_card_border_thickness | divided_by: 10.0 }}rem;
        --blog-card-border-opacity: {{ settings.blog_card_border_opacity | divided_by: 100.0 }};
        --blog-card-shadow-opacity: {{ settings.blog_card_shadow_opacity | divided_by: 100.0 }};
        --blog-card-shadow-visible: {% if settings.blog_card_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --blog-card-shadow-horizontal-offset: {{ settings.blog_card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --blog-card-shadow-vertical-offset: {{ settings.blog_card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --blog-card-shadow-blur-radius: {{ settings.blog_card_shadow_blur | divided_by: 10.0 }}rem;

        --badge-corner-radius: {{ settings.badge_corner_radius | divided_by: 10.0 }}rem;

        --popup-border-width: {{ settings.popup_border_thickness }}px;
        --popup-border-opacity: {{ settings.popup_border_opacity | divided_by: 100.0 }};
        --popup-corner-radius: {{ settings.popup_corner_radius }}px;
        --popup-shadow-opacity: {{ settings.popup_shadow_opacity | divided_by: 100.0 }};
        --popup-shadow-horizontal-offset: {{ settings.popup_shadow_horizontal_offset }}px;
        --popup-shadow-vertical-offset: {{ settings.popup_shadow_vertical_offset }}px;
        --popup-shadow-blur-radius: {{ settings.popup_shadow_blur }}px;

        --drawer-border-width: {{ settings.drawer_border_thickness }}px;
        --drawer-border-opacity: {{ settings.drawer_border_opacity | divided_by: 100.0 }};
        --drawer-shadow-opacity: {{ settings.drawer_shadow_opacity | divided_by: 100.0 }};
        --drawer-shadow-horizontal-offset: {{ settings.drawer_shadow_horizontal_offset }}px;
        --drawer-shadow-vertical-offset: {{ settings.drawer_shadow_vertical_offset }}px;
        --drawer-shadow-blur-radius: {{ settings.drawer_shadow_blur }}px;

        --spacing-sections-desktop: {{ settings.spacing_sections }}px;
        --spacing-sections-mobile: {% if settings.spacing_sections < 24 %}{{ settings.spacing_sections }}{% else %}{{ settings.spacing_sections | times: 0.7 | round | at_least: 20 }}{% endif %}px;

        --grid-desktop-vertical-spacing: {{ settings.spacing_grid_vertical }}px;
        --grid-desktop-horizontal-spacing: {{ settings.spacing_grid_horizontal }}px;
        --grid-mobile-vertical-spacing: {{ settings.spacing_grid_vertical_mobile }}px;
        --grid-mobile-horizontal-spacing: {{ settings.spacing_grid_horizontal_mobile }}px;

        --text-boxes-border-opacity: {{ settings.text_boxes_border_opacity | divided_by: 100.0 }};
        --text-boxes-border-width: {{ settings.text_boxes_border_thickness }}px;
        --text-boxes-radius: {{ settings.text_boxes_radius }}px;
        --text-boxes-shadow-opacity: {{ settings.text_boxes_shadow_opacity | divided_by: 100.0 }};
        --text-boxes-shadow-visible: {% if settings.text_boxes_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --text-boxes-shadow-horizontal-offset: {{ settings.text_boxes_shadow_horizontal_offset }}px;
        --text-boxes-shadow-vertical-offset: {{ settings.text_boxes_shadow_vertical_offset }}px;
        --text-boxes-shadow-blur-radius: {{ settings.text_boxes_shadow_blur }}px;

        --buttons-radius: {{ settings.buttons_radius }}px;
        --buttons-radius-outset: {% if settings.buttons_radius > 0 %}{{ settings.buttons_radius | plus: settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-width: {% if settings.buttons_border_opacity > 0 %}{{ settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-opacity: {{ settings.buttons_border_opacity | divided_by: 100.0 }};
        --buttons-shadow-opacity: {{ settings.buttons_shadow_opacity | divided_by: 100.0 }};
        --buttons-shadow-visible: {% if settings.buttons_shadow_opacity > 0 %}1{% else %}0{% endif %};
        --buttons-shadow-horizontal-offset: {{ settings.buttons_shadow_horizontal_offset }}px;
        --buttons-shadow-vertical-offset: {{ settings.buttons_shadow_vertical_offset }}px;
        --buttons-shadow-blur-radius: {{ settings.buttons_shadow_blur }}px;
        --buttons-border-offset: {% if settings.buttons_radius > 0 or settings.buttons_shadow_opacity > 0 %}0.3{% else %}0{% endif %}px;

        {% comment %}
          bs-add
          - added new button setup here
        {% endcomment %}
        --button-1-border-color: {{ settings.button-1-border-color.red }}, {{ settings.button-1-border-color.green }}, {{ settings.button-1-border-color.blue }};
        --button-1-background-color: {{ settings.button-1-background-color.red }}, {{ settings.button-1-background-color.green }}, {{ settings.button-1-background-color.blue }};
        --button-1-text-color: {{ settings.button-1-text-color.red }}, {{ settings.button-1-text-color.green }}, {{ settings.button-1-text-color.blue }};
        --button-2-border-color: {{ settings.button-2-border-color.red }}, {{ settings.button-2-border-color.green }}, {{ settings.button-2-border-color.blue }};
        --button-2-background-color: {{ settings.button-2-background-color.red }}, {{ settings.button-2-background-color.green }}, {{ settings.button-2-background-color.blue }};
        --button-2-text-color: {{ settings.button-2-text-color.red }}, {{ settings.button-2-text-color.green }}, {{ settings.button-2-text-color.blue }};
        --button-3-border-color: {{ settings.button-3-border-color.red }}, {{ settings.button-3-border-color.green }}, {{ settings.button-3-border-color.blue }};
        --button-3-background-color: {{ settings.button-3-background-color.red }}, {{ settings.button-3-background-color.green }}, {{ settings.button-3-background-color.blue }};
        --button-3-text-color: {{ settings.button-3-text-color.red }}, {{ settings.button-3-text-color.green }}, {{ settings.button-3-text-color.blue }};
        --button-4-border-color: {{ settings.button-4-border-color.red }}, {{ settings.button-4-border-color.green }}, {{ settings.button-4-border-color.blue }};
        --button-4-background-color: {{ settings.button-4-background-color.red }}, {{ settings.button-4-background-color.green }}, {{ settings.button-4-background-color.blue }};
        --button-4-text-color: {{ settings.button-4-text-color.red }}, {{ settings.button-4-text-color.green }}, {{ settings.button-4-text-color.blue }};
        --button-5-border-color: {{ settings.button-5-border-color.red }}, {{ settings.button-5-border-color.green }}, {{ settings.button-5-border-color.blue }};
        --button-5-background-color: {{ settings.button-5-background-color.red }}, {{ settings.button-5-background-color.green }}, {{ settings.button-5-background-color.blue }};
        --button-5-text-color: {{ settings.button-5-text-color.red }}, {{ settings.button-5-text-color.green }}, {{ settings.button-5-text-color.blue }};

        --inputs-radius: {{ settings.inputs_radius }}px;
        --inputs-textarea-radius: {{ settings.inputs_textarea_radius }}px;
        --inputs-border-width: {{ settings.inputs_border_thickness }}px;
        --inputs-border-opacity: {{ settings.inputs_border_opacity | divided_by: 100.0 }};
        --inputs-shadow-opacity: {{ settings.inputs_shadow_opacity | divided_by: 100.0 }};
        --inputs-shadow-horizontal-offset: {{ settings.inputs_shadow_horizontal_offset }}px;
        --inputs-margin-offset: {% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_opacity > 0 %}{{ settings.inputs_shadow_vertical_offset | abs }}{% else %}0{% endif %}px;
        --inputs-shadow-vertical-offset: {{ settings.inputs_shadow_vertical_offset }}px;
        --inputs-shadow-blur-radius: {{ settings.inputs_shadow_blur }}px;
        --inputs-radius-outset: {% if settings.inputs_radius > 0 %}{{ settings.inputs_radius | plus: settings.inputs_border_thickness }}{% else %}0{% endif %}px;
        --inputs-textarea-radius-outset: {% if settings.inputs_textarea_radius > 0 %}{{ settings.inputs_textarea_radius | plus: settings.inputs_border_thickness }}{% else %}0{% endif %}px;

        --variant-pills-radius: {{ settings.variant_pills_radius }}px;
        --variant-pills-border-width: {{ settings.variant_pills_border_thickness }}px;
        --variant-pills-border-opacity: {{ settings.variant_pills_border_opacity | divided_by: 100.0 }};
        --variant-pills-shadow-opacity: {{ settings.variant_pills_shadow_opacity | divided_by: 100.0 }};
        --variant-pills-shadow-horizontal-offset: {{ settings.variant_pills_shadow_horizontal_offset }}px;
        --variant-pills-shadow-vertical-offset: {{ settings.variant_pills_shadow_vertical_offset }}px;
        --variant-pills-shadow-blur-radius: {{ settings.variant_pills_shadow_blur }}px;

        {% comment %}
          bs-add
          - added for color swatches
        {% endcomment %}
        --variant-color-pills-radius: {{ settings.variant_color_pills_radius }}px;
        --variant-color-pills-border-width: {{ settings.variant_color_pills_border_thickness }}px;
        --variant-color-pills-border-opacity: {{ settings.variant_color_pills_border_opacity | divided_by: 100.0 }};
        --variant-color-pills-shadow-opacity: {{ settings.variant_color_pills_shadow_opacity | divided_by: 100.0 }};
        --variant-color-pills-shadow-horizontal-offset: {{ settings.variant_color_pills_shadow_horizontal_offset }}px;
        --variant-color-pills-shadow-vertical-offset: {{ settings.variant_color_pills_shadow_vertical_offset }}px;
        --variant-color-pills-shadow-blur-radius: {{ settings.variant_color_pills_shadow_blur }}px;
      }

      *,
      *::before,
      *::after {
        box-sizing: inherit;
      }

      html {
        box-sizing: border-box;
        font-size: calc(var(--font-body-scale) * 62.5%);
        height: 100%;
      }

      body {
        display: grid;
        grid-template-rows: auto auto 1fr auto;
        grid-template-columns: 100%;
        min-height: 100%;
        margin: 0;
        font-size: 1.5rem;
        letter-spacing: 0.06rem;
        line-height: calc(1 + 0.8 / var(--font-body-scale));
        font-family: var(--font-body-family);
        font-style: var(--font-body-style);
        font-weight: var(--font-body-weight);
      }

      @media screen and (min-width: 750px) {
        body {
          font-size: 1.6rem;
        }
      }
    {% endstyle %}

    {% comment %}
        bs-add
          - Support for custom typography (overrides Dawn defaults)
      {% endcomment %}
    {% style %}
      {{ settings.custom_fonts }}
    {% endstyle %}

    {% style %}
      {{ settings.button-custom-css }}
      {{ settings.button-1-custom-css }}
      {{ settings.button-2-custom-css }}
      {{ settings.button-3-custom-css }}
      {{ settings.button-4-custom-css }}
      {{ settings.button-5-custom-css }}
    {% endstyle %}

    {% style %}
      {{ settings.global_custom_css }}
    {% endstyle %}

    {%- unless settings.type_body_font.system? -%}
      {%- if settings.type_body_font_family == blank -%}
        {% comment %}theme-check-disable AssetPreload{% endcomment %}
        <link rel="preload" as="font" href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
        {% comment %}theme-check-enable AssetPreload{% endcomment %}
      {%- endif -%}
    {%- endunless -%}
    {%- unless settings.type_header_font.system? -%}
      {%- if settings.type_heading_font_family == blank %}
        {% comment %}theme-check-disable AssetPreload{% endcomment %}
        <link rel="preload" as="font" href="{{ settings.type_header_font | font_url }}" type="font/woff2" crossorigin>
        {% comment %}theme-check-enable AssetPreload{% endcomment %}
      {%- endif -%}
    {%- endunless -%}

    {{ 'template-giftcard.css' | asset_url | stylesheet_tag }}

    <script>
      {{ settings.global_custom_js }}
    </script>
  </head>

  {% comment %}
    bs-mod
    - color and gradient options set on body now
  {% endcomment %}
  <body class="gift-card color-{{ settings.theme_color_scheme }} gradient bs-type-{{ request.page_type }} bs-template-{{ page.template_suffix | default: 'default' }}">
    <header>
      <div class="gift-card__price">
        <h1>
          {% if settings.currency_code_enabled %}
            {{ gift_card.balance | money_with_currency }}
          {% else %}
            {{ gift_card.balance | money }}
          {% endif %}
        </h1>
        {%- if gift_card.enabled == false or gift_card.expired -%}
          <p class="badge badge--expired">{{ 'gift_cards.issued.expired' | t }}</p>
        {%- endif -%}
      </div>
      {% if gift_card.expires_on %}
        {%- assign gift_card_expiration_date = gift_card.expires_on | date: '%B %e, %Y' -%}
        <p class="gift-card__text">
          {{ 'gift_cards.issued.expiration_date' | t: expires_on: gift_card_expiration_date }}
        </p>
      {% endif %}
    </header>

    <main>
      <div class="gift-card__image-wrapper">
        {%- if settings.logo != blank -%}
          {%- assign logo_alt = settings.logo.alt | default: shop.name | escape -%}
          {{ settings.logo | image_url: width: 570 | image_tag: class: 'gift-card__image', alt: logo_alt }}
        {%- else %}
          <img
            class="gift-card__image"
            src="{{ 'gift-card/card.svg' | shopify_asset_url }}"
            alt=""
            height="{{ 570 | divided_by: 1.5 }}"
            width="570"
            loading="eager"
          >
        {%- endif %}
      </div>
      <h2>{{ shop.name }}</h2>
      <div class="gift-card__text-wrapper">
        <p class="gift-card__text">{{ 'gift_cards.issued.how_to_use_gift_card' | t }}</p>
      </div>
      <p id="gift-card-code" class="gift-card__number">{{ gift_card.code | format_code }}</p>
      <div class="gift-card__qr-code" data-identifier="{{ gift_card.qr_identifier }}"></div>
      {%- if gift_card.pass_url -%}
        <a href="{{ gift_card.pass_url }}" class="gift_card__apple-wallet no-print">
          <img
            src="{{ 'gift-card/add-to-apple-wallet.svg' | shopify_asset_url }}"
            width="120"
            height="40"
            alt="{{ 'gift_cards.issued.add_to_apple_wallet' | t }}"
            loading="lazy"
          >
        </a>
      {%- endif -%}
      <div class="gift-card__buttons no-print">
        <span class="gift-card__copy-success form__message" role="status"></span>
        <template>
          <span class="svg-wrapper">
            {{- 'icon-success.svg' | inline_asset_content -}}
          </span>
          {{ 'gift_cards.issued.copy_code_success' | t }}
        </template>
        <button class="button gift-card__copy-button">{{ 'gift_cards.issued.copy_code' | t }}</button>
        <a
          href="{{ shop.url }}"
          class="button button--secondary"
          target="_blank"
          rel="noopener"
          aria-describedby="a11y-new-window-message"
        >
          {{ 'gift_cards.issued.shop_link' | t }}
        </a>
      </div>
    </main>

    <div hidden>
      <span id="a11y-new-window-message">{{ 'accessibility.link_messages.new_window' | t }}</span>
    </div>
  </body>
</html>

<script>
  var string = { qrImageAlt: {{ 'gift_cards.issued.qr_image_alt' | t | json }} };
  document.addEventListener('DOMContentLoaded', function() {
   new QRCode( document.querySelector('.gift-card__qr-code'), {
    text: document.querySelector('.gift-card__qr-code').dataset.identifier,
    width: 72,
    height: 72,
    imageAltText: string.qrImageAlt
    });
  });
  var template = document.getElementsByTagName("template")[0];
  var clonedTemplate = template.content.cloneNode(true);
  var isMessageDisplayed = false
  document
  .querySelector('.gift-card__copy-button')
  .addEventListener('click', () => {
    navigator.clipboard.writeText(document.getElementById('gift-card-code').innerText).then(function () {
      if (!isMessageDisplayed) {
        document.querySelector('.gift-card__copy-success').appendChild(clonedTemplate);
        isMessageDisplayed = true
      }
    });
  });
</script>