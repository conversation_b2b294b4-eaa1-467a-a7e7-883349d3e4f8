/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "custom_liquid_8RmczQ": {
          "type": "custom_liquid",
          "settings": {
            "title": "My custom liquid",
            "custom_liquid": "<h1 class=\"h2\">DownShift<br>Dungeon</h1>\n<h5>+ 12 pack of DownShift<h5>",
            "hide_size": ""
          }
        },
        "text_TDURcb": {
          "type": "text",
          "settings": {
            "text": "A limited-edition lockable cage that’s perfect for keeping your DownShift drinks secure from thirsty roommates, pranksters, or anyone else lurking in the night! ",
            "text_liquid": "",
            "text_style": "body",
            "hide_size": ""
          }
        },
        "text_MmL8nP": {
          "type": "text",
          "settings": {
            "text": "<em>Includes a lockable </em>refrigerator<em> cage and </em><a href=\"https://enjoyshift.com/products/downshift-dungeon\" title=\"https://enjoyshift.com/products/downshift-dungeon\"><em>12 can variety pack of DownShift</em></a><em>.</em>",
            "text_liquid": "",
            "text_style": "body",
            "hide_size": ""
          }
        },
        "custom_liquid_HjUhpf": {
          "type": "custom_liquid",
          "disabled": true,
          "settings": {
            "title": "My custom liquid",
            "custom_liquid": "<ul style=\"padding-left: 0;\">\n<li><b>Includes:<span>&nbsp;</span></b>12-pack Variety Case of DownShift</li>\n<li><b>Lockable &amp; Durable Design:</b><span>&nbsp;</span>Crafted to keep your DownShift safe and sound.</li>\n<li><b>Spooky Halloween Theme:</b><span>&nbsp;</span>Perfect for the season—scare off drink snatchers with style!</li>\n<li><b>Portable Size:</b><span>&nbsp;</span>Fits neatly in most refrigerators or on shelves. Comfortably fits a 6Pack of DownShift</li>\n<li><b>Limited Edition:</b><span>&nbsp;</span>Available only this Halloween season—get it before it vanishes!</li>\n</ul>",
            "hide_size": ""
          }
        },
        "price_yhd3pk": {
          "type": "price",
          "settings": {
            "custom_above_taxes_liquid": "",
            "show_taxes_included": false,
            "show_duties_included": false,
            "show_shipping_policy": false,
            "show_payment_terms": false,
            "hide_size": ""
          }
        },
        "buy_buttons_BnCHQj": {
          "type": "buy_buttons",
          "settings": {
            "show_dynamic_checkout": false,
            "custom_liquid_buttons": "",
            "button_style": "button button-2",
            "show_gift_card_recipient": false,
            "hide_size": ""
          }
        }
      },
      "block_order": [
        "custom_liquid_8RmczQ",
        "text_TDURcb",
        "text_MmL8nP",
        "custom_liquid_HjUhpf",
        "price_yhd3pk",
        "buy_buttons_BnCHQj"
      ],
      "disabled": true,
      "custom_css": [
        "@media screen and (min-width: 750px) {img, video {clip-path: polygon( 0% 15%, 15% 15%, 15% 0%, 85% 0%, 85% 15%, 100% 15%, 95% 71%, 100% 100%, 85% 100%, 15% 100%, 0 100%, 5% 73% ); } .product {align-items: center; }}"
      ],
      "settings": {
        "enable_sticky_info": false,
        "color_scheme": "background-1",
        "media_size": "small",
        "constrain_to_viewport": true,
        "media_fit": "contain",
        "gallery_layout": "stacked",
        "media_position": "right",
        "image_zoom": "none",
        "mobile_thumbnails": "hide",
        "hide_variants": true,
        "enable_video_looping": false,
        "accordion_padding": 0.5,
        "accordion_title_size": "h6",
        "padding_top_mobile": 16,
        "padding_bottom_mobile": 16,
        "padding_top": 40,
        "padding_bottom": 40,
        "custom_liquid_column": "",
        "custom_column_placement": "first",
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "multicolumn_fzENrm": {
      "type": "multicolumn",
      "blocks": {
        "column_h8B9mD": {
          "type": "column",
          "settings": {
            "title": "",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<div id=\"container\">\n            <div id=\"spooky\">\n                <div id=\"body\">\n                    <div id=\"eyes\"></div>\n                    <div id=\"mouth\"></div>\n                    <div id=\"feet\">\n                        <div></div>\n                        <div></div>\n                        <div></div>\n                    </div>\n                </div>\n            </div>\n            <div id=\"shadow\"></div>\n        </div>\n\n<style>\n#container {\n    overflow: hidden;\n    width: 100%;\nheight; 100%;\n    display: flex;\nflex-direction: column;\n    justify-content: center;\n    align-items: center;\n    margin: 0;\n}\n\n@media screen and (min-width: 750px) {\n#container {\npadding-top: 150px;\npadding-bottom: 150px;\n}\n}\n\n#spooky {\n    margin: 20px auto;\n    width: 80%;\n    height: 80%;\n    animation: floaty 2s infinite;\n}\n\n#spooky #body {\n    position: relative;\n    margin: auto;\n    width: 180px;\n    height: 220px;\n    background: #98DE59;\n    border-top-left-radius: 90px;\n    border-top-right-radius: 90px;\n}\n\n#spooky #body:before,\n#spooky #body:after {\n    content: '';\n    position: absolute;\n    top: 130px;\n    display: inline-block;\n    width: 36px;\n    height: 36px;\n    border-radius: 50%;\n    background: #98DE59;\n    animation: floaty .2s infinite;\n}\n\n#spooky #body:before {\n    left: -18px;\n}\n\n#spooky #body:after {\n    right: -18px;\n}\n\n#spooky #body #eyes {\n    display: flex;\n    justify-content: space-between;\n    margin: 0 auto;\n    padding: 90px 0 0;\n    width: 90px;\n    height: 20px;\n}\n\n#spooky #body #eyes:before,\n#spooky #body #eyes:after {\n    content: ' ';\n    display: block;\n    width: 30px;\n    height: 30px;\n    background: #2B2D20;\n    border-radius: 50%;\n}\n\n#spooky #body #mouth {\n    background: #2B2D20;\n    margin: 25px auto 0;\n    width: 60px;\n    height: 30px;\n    border-bottom-left-radius: 30px;\n    border-bottom-right-radius: 30px;\ndisplay: block;\n}\n\n#spooky #body #mouth:before {\n    content: ' ';\n    display: block;\n    background: #98DE59;\n    margin-left: 20px;\n    width: 10px;\n    height: 10px;\n}\n\n#spooky #body #feet {\n    position: absolute;\n    display: flex;\n    bottom: -18px;\n    width: 180px;\n    height: 36px;\n}\n\n#spooky #body #feet > *,\n#spooky #body #feet::before,\n#spooky #body #feet::after {\n    content: ' ';\n    width: 36px;\n    height: 36px;\n    background: #98DE59;\n    border-radius: 50%;\ndisplay: inline !important;\n}\n\n#shadow {\n    margin: 0 30px 0;\n    background: #D7DBC6;\n    width: 180px;\n    height: 40px;\n    border-radius: 50%;\n    animation: zoomy 2s infinite;\ndisplay: inline;\n}\n\n@keyframes floaty {\n    0%, 100% {\n        transform: translateY(0);\n    }\n\n    50% {\n        transform: translateY(-20px);\n    }\n}\n\n@keyframes zoomy {\n    0%, 100% {\n        transform: scale(1);\n    }\n\n    50% {\n        transform: scale(0.8);\n    }\n}\n</style>",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "column_neQtCq": {
          "type": "column",
          "settings": {
            "title": "<strong>(kinda) Safe Seltzer</strong>",
            "title_liquid": "",
            "text": "<p>The DownShift Dungeon is a limited-edition lockable cage that’s perfect for keeping your DownShift drinks secure from thirsty roommates, pranksters, or anyone else lurking in the night! </p><p></p><p>The DownShift Dungeon is sold out. It might be back someday.</p><p></p><p>Now <a href=\"/products/down-shift-variety-pack\" title=\"DownShift Variety Pack\">go buy some DownShift</a>.</p>",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "column_h8B9mD",
        "column_neQtCq"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "background-1",
        "color_scheme_content": "background-2",
        "background_style": "none",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 2,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 250,
        "padding_bottom_desktop": 250,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": false,
        "page": "",
        "custom_css_class": "",
        "custom_liquid": "<style>\n#shopify-section-{{ section.id }} {\nborder-bottom: 1px solid #D7DBC6;\n}\n.multicolumn-list {\nalign-items: center;\n}\n</style>"
      }
    },
    "custom_liquid_rTz8wK": {
      "type": "custom-liquid",
      "disabled": true,
      "settings": {
        "description": "",
        "custom_liquid": "<div id=\"container\">\n            <div id=\"spooky\">\n                <div id=\"body\">\n                    <div id=\"eyes\"></div>\n                    <div id=\"mouth\"></div>\n                    <div id=\"feet\">\n                        <div></div>\n                        <div></div>\n                        <div></div>\n                    </div>\n                </div>\n            </div>\n            <div id=\"shadow\"></div>\n        </div>\n\n<style>\n#container {\n    overflow: hidden;\n    width: 100%;\n    display: flex;\nflex-direction: column;\n    justify-content: center;\n    align-items: center;\n    margin: 0;\n}\n#spooky {\n    margin: 20px auto;\n    width: 80%;\n    height: 80%;\n    animation: floaty 2s infinite;\n}\n\n#spooky #body {\n    position: relative;\n    margin: 50px auto 0;\n    width: 180px;\n    height: 220px;\n    background: #98DE59;\n    border-top-left-radius: 90px;\n    border-top-right-radius: 90px;\n}\n\n#spooky #body:before,\n#spooky #body:after {\n    content: '';\n    position: absolute;\n    top: 130px;\n    display: inline-block;\n    width: 36px;\n    height: 36px;\n    border-radius: 50%;\n    background: #98DE59;\n    animation: floaty .2s infinite;\n}\n\n#spooky #body:before {\n    left: -18px;\n}\n\n#spooky #body:after {\n    right: -18px;\n}\n\n#spooky #body #eyes {\n    display: flex;\n    justify-content: space-between;\n    margin: 0 auto;\n    padding: 90px 0 0;\n    width: 90px;\n    height: 20px;\n}\n\n#spooky #body #eyes:before,\n#spooky #body #eyes:after {\n    content: ' ';\n    display: block;\n    width: 30px;\n    height: 30px;\n    background: #2B2D20;\n    border-radius: 50%;\n}\n\n#spooky #body #mouth {\n    background: #2B2D20;\n    margin: 25px auto 0;\n    width: 60px;\n    height: 30px;\n    border-bottom-left-radius: 30px;\n    border-bottom-right-radius: 30px;\n}\n\n#spooky #body #mouth:before {\n    content: ' ';\n    display: block;\n    background: #98DE59;\n    margin-left: 20px;\n    width: 10px;\n    height: 10px;\n}\n\n#spooky #body #feet {\n    position: absolute;\n    display: flex;\n    bottom: -18px;\n    width: 180px;\n    height: 36px;\n}\n\n#spooky #body #feet > *,\n#spooky #body #feet::before,\n#spooky #body #feet::after {\n    content: ' ';\n    width: 36px;\n    height: 36px;\n    background: #98DE59;\n    border-radius: 50%;\ndisplay: inline !important;\n}\n\n#shadow {\n    margin: 0 30px 0;\n    background: #252c49;\n    width: 180px;\n    height: 40px;\n    border-radius: 50%;\n    animation: zoomy 2s infinite;\n}\n\n@keyframes floaty {\n    0%, 100% {\n        transform: translateY(0);\n    }\n\n    50% {\n        transform: translateY(-20px);\n    }\n}\n\n@keyframes zoomy {\n    0%, 100% {\n        transform: scale(1);\n    }\n\n    50% {\n        transform: scale(0.8);\n    }\n}\n</style>",
        "hide_size": "",
        "color_scheme": "background-1",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 95
      }
    },
    "related-products": {
      "type": "related-products",
      "disabled": true,
      "settings": {
        "heading": "You may also like",
        "heading_liquid": "",
        "heading_size": "h2",
        "products_to_show": 4,
        "columns_desktop": 4,
        "color_scheme": "background-2",
        "image_ratio": "square",
        "image_shape": "default",
        "show_secondary_image": "true",
        "show_vendor": "false",
        "show_rating": "false",
        "quick_add": "none",
        "quick_add_behavior": "",
        "card_heading_font_size": "h5",
        "quick_add_button_style": "button button--primary",
        "show_color_swatches": "false",
        "show_alternative_title": "false",
        "show_card_product_custom_field": "",
        "columns_mobile": "2",
        "recommendation_type": "related",
        "recommendation_product_list": [

        ],
        "recommendation_collection": "",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 36,
        "padding_bottom_desktop": 36,
        "hide_size": ""
      }
    }
  },
  "order": [
    "main",
    "multicolumn_fzENrm",
    "custom_liquid_rTz8wK",
    "related-products"
  ]
}
