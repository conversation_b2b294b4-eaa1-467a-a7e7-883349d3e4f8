{%- liquid
  assign block_index = 0
  for section_block in section.blocks
    assign block_index = block_index | plus: 1
    if block.id == section_block.id
      break
    endif
  endfor
-%}

<button class="tabbed-content-tab {{ section.settings.tab_button_custom_css }}" role="tab" aria-controls="panel{{ block_index }}" {% if block_index == 1 %}aria-selected="true"{% endif %}>{{ block.settings.title }}</button>

<div 
  id="panel{{ block_index }}" 
  role="tabpanel" 
  {% unless block_index == 1 %}hidden{% endunless %}
  {% if settings.animations_reveal_on_scroll %}
    data-cascade
    style="--animation-order: {{ block_index }};"
  {% endif %}
  class="{{ block.settings.custom_classes }} {% if block.settings.custom_color_scheme %}color-{{ block.settings.color_scheme }}{% endif %}"
  {{ block.shopify_attributes }}
>
  {% content_for 'blocks' %}
</div>

{% schema %}
{
  "name": "Tab",
  "tag": null,
  "blocks": [{ "type": "@theme" }, { "type": "@app" }],
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Tab title"
    },
    {
      "type": "header",
      "content": "Color"
    },
    {
      "type": "checkbox",
      "id": "custom_color_scheme",
      "label": "Enable block color scheme",
      "info": "If unchecked, tab will inherit color scheme",
      "default": false
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "Color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "text",
      "id": "custom_classes",
      "label": "Block custom classes"
    }
  ],
  "presets": [
    {
      "name": "Tab"
    }
  ]
}
{% endschema %}
