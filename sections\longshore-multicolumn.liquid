{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif

  # bs-add
  # - support for desktop slider
  assign columns_desktop_int = section.settings.columns_desktop | plus: 0
  assign show_desktop_slider = false
  if section.settings.swipe_on_desktop and section.blocks.size > columns_desktop_int
    assign show_desktop_slider = true
  endif
-%}

<style>
  /* Option to have section take up half the width so you can put sectinos side by side */
  @media screen and (min-width: 750px) {
    #shopify-section-{{ section.id }}:has(.longshore-multicolumn-full) {
      width: 100%;
      max-width: 100%;
    }
    #shopify-section-{{ section.id }}:has(.longshore-multicolumn-half) {
      width: 50%;
      max-width: 50%;
      display: inline-block;
      vertical-align: top;
    }
    #shopify-section-{{ section.id }}:has(.longshore-multicolumn-one-third) {
      width: 33.333%;
      max-width: 33.333%;
      display: inline-block;
      vertical-align: top;
    }
    #shopify-section-{{ section.id }}:has(.longshore-multicolumn-two-thirds) {
      width: 66.666%;
      max-width: 66.666%;
      display: inline-block;
      vertical-align: top;
    }
  }

  /* Override slider margins */

  @media screen and (max-width: 749px) {
    {% if section.settings.mobile_page_width %}
      {% if section.settings.swipe_on_mobile %}
        {% if section.settings.mobile_slider_peek %}
          #shopify-section-{{ section.id }} slider-component {
            max-width: none;
            margin-left: 0;
            scroll-margin-left: 0;
            scroll-snap-align: center;
          }
          #shopify-section-{{ section.id }} .slider .slider__slide:first-child {
            margin-left: 1.5rem;
            scroll-margin-left: 1.5rem;
          }
          #shopify-section-{{ section.id }} .longshore-multicolumn .slider .grid__item.slider__slide {
            width: calc(80% / {{ section.settings.columns_mobile }});
            scroll-snap-align: center;
          }
        {% else %}
          #shopify-section-{{ section.id }} slider-component {
            max-width: none;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
          }
        {% endif %}
      {% else %}
        #shopify-section-{{ section.id }} slider-component {
          max-width: var(--page-width);
          margin: 0 auto;
          padding: 0 1.5rem;
        }
      {% endif %}
    {% else %}
      #shopify-section-{{ section.id }} .slider .slider__slide:first-child {
        margin-left: 0;
        scroll-margin-left: 0;
        scroll-snap-align: center;
      }
      #shopify-section-{{ section.id }} .content-container {
        --text-boxes-radius: 0;
      }
      {% if section.settings.mobile_slider_peek %}
        #shopify-section-{{ section.id }} .longshore-multicolumn .slider .grid__item.slider__slide {
          width: calc(90% / {{ section.settings.columns_mobile }});
          scroll-snap-align: center;
        }
      {% endif %}

    {% endif %}
  }

  @media screen and (min-width: 750px) {
    #shopify-section-{{ section.id }} .longshore-multicolumn .slider {
      scroll-padding-left: 0;
    }
    {% if section.settings.desktop_page_width %}
      {% if section.settings.swipe_on_desktop %}
        {% if section.settings.desktop_slider_peek %}
          #shopify-section-{{ section.id }} .slider--desktop .slider__slide:first-child {
            scroll-margin-left: var(--desktop-margin-left-first-item);
            margin-left: var(--desktop-margin-left-first-item);
          }
          #shopify-section-{{ section.id }} .longshore-multicolumn .slider .grid__item.slider__slide {
            width: calc(80% / {{ section.settings.columns_desktop }});
            scroll-snap-align: center;
          }
        {% else %}
          #shopify-section-{{ section.id }} .slider .slider__slide:first-child {
            scroll-margin-left: 0;
            margin-left: 0;
          }
          #shopify-section-{{ section.id }} slider-component {
            max-width: var(--page-width);
            margin: 0 auto;
            padding: 0 5rem;
          }
        {% endif %}

      {% else %}
        #shopify-section-{{ section.id }} slider-component {
          max-width: var(--page-width);
          margin: 0 auto;
          padding: 0 5rem;
        }
      {% endif %}
    {% else %}
      #shopify-section-{{ section.id }} .slider .slider__slide:first-child {
        margin-left: 0;
        scroll-margin-left: 0;
      }
      #shopify-section-{{ section.id }} .content-container {
        --text-boxes-radius: 0;
      }
      {% if section.settings.desktop_slider_peek %}
        #shopify-section-{{ section.id }} .longshore-multicolumn .slider .grid__item.slider__slide {
          width: calc(90% / {{ section.settings.columns_desktop }});
          scroll-snap-align: center;
        }
      {% endif %}
    {% endif %}
  }

  .longshore-multicolumn .grid.slider::after,
  .longshore-multicolumn .grid.slider--desktop::after,
  .longshore-multicolumn .grid.slider--tablet::after {
    content: none;
  }
</style>

<div
  class="longshore-multicolumn tw:relative color-{{ section.settings.color_scheme_section }} gradient {{ section.settings.section_width }} section-{{ section.id }}-padding {{ section.settings.parent_div_custom_css }} {% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
  {% if settings.animations_reveal_on_scroll %}
    data-cascade
  {% endif %}
>
  {{ section.settings.custom_liquid_before }}
  <slider-component
    class="{{ section.settings.slider_component_custom_css }}"
  >
    <ul
      class="longshore-multicolumn-list color-{{ section.settings.color_scheme_slider }} gradient content-container contains-content-container grid grid--{{ section.settings.columns_mobile }}-col grid--{{ section.settings.columns_desktop }}-col-tablet {% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet{% endif %}{% endif %} tw:m-0! {{ section.settings.ul_custom_css }}"
      id="Slider-{{ section.id }}"
      role="list"
    >
      {% content_for 'blocks' %}
    </ul>

    {%- if show_mobile_slider or show_desktop_slider -%}
      <div class="slider-buttons {{ section.settings.slider_controls_css }}">
        <button
          type="button"
          class="slider-button slider-button--prev"
          name="previous"
          aria-label="{{ 'general.slider.previous_slide' | t }}"
        >
          <span class="svg-wrapper">{{ 'icon-caret.svg' | inline_asset_content }}</span>
        </button>
        <button
          type="button"
          class="slider-button slider-button--next"
          name="next"
          aria-label="{{ 'general.slider.next_slide' | t }}"
        >
          <span class="svg-wrapper">{{ 'icon-caret.svg' | inline_asset_content }}</span>
        </button>
      </div>
    {%- endif -%}
  </slider-component>
  {{ section.settings.custom_liquid_after }}
</div>

{% schema %}
{
  "name": "Supercolumn",
  "class": "longshore-multicolumn",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Color Scheme"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_section",
      "label": "Section color scheme",
      "default": "background-1"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_slider",
      "label": "Slider color scheme",
      "default": "background-1"
    },
    {
      "type": "header",
      "content": "Mobile layout",
      "info": "Devices with screens < 750px"
    },
    {
      "type": "checkbox",
      "id": "mobile_page_width",
      "default": true,
      "label": "Use margins"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "1 column"
        },
        {
          "value": "2",
          "label": "2 columns"
        }
      ],
      "default": "2",
      "label": "Columns"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "Enable slider"
    },
    {
      "type": "checkbox",
      "id": "mobile_slider_peek",
      "default": false,
      "label": "Peek next slide?"
    },
    {
      "type": "number",
      "id": "padding_top_mobile",
      "label": "Padding top mobile"
    },
    {
      "type": "number",
      "id": "padding_bottom_mobile",
      "label": "Padding bottom mobile"
    },
    {
      "type": "header",
      "content": "Desktop layout",
      "info": "Devices with screens >= 750px"
    },
    {
      "type": "checkbox",
      "id": "desktop_page_width",
      "default": true,
      "label": "Use margins"
    },
    {
      "type": "select",
      "id": "section_width",
      "options": [
        {
          "value": "longshore-multicolumn-full",
          "label": "Full width"
        },
        {
          "value": "longshore-multicolumn-half",
          "label": "Half width"
        },
        {
          "value": "longshore-multicolumn-one-third",
          "label": "One third"
        },
        {
          "value": "longshore-multicolumn-two-thirds",
          "label": "Two thirds"
        }
      ],
      "default": "longshore-multicolumn-full",
      "label": "Section width"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "Columns"
    },
    {
      "type": "checkbox",
      "id": "swipe_on_desktop",
      "default": false,
      "label": "Enable swipe on desktop"
    },
    {
      "type": "checkbox",
      "id": "desktop_slider_peek",
      "default": false,
      "label": "Peek next slide?"
    },
    {
      "type": "number",
      "id": "padding_top_desktop",
      "label": "Padding top tablet/desktop"
    },
    {
      "type": "number",
      "id": "padding_bottom_desktop",
      "label": "Padding bottom tablet/desktop"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "text",
      "id": "parent_div_custom_css",
      "label": "Custom CSS classes for top <div>",
      "info": "The highest level component we can pass CSS classes to"
    },
    {
      "type": "text",
      "id": "slider_component_custom_css",
      "label": "Custom CSS classes for <slider-component>"
    },
    {
      "type": "text",
      "id": "ul_custom_css",
      "label": "Custom CSS classes for <ul> component"
    },
    {
      "type": "text",
      "id": "slider_controls_css",
      "label": "Custom CSS classes for slider controls"
    },
    {
      "type": "liquid",
      "id": "custom_liquid_before",
      "label": "Custom liquid before (loads before <slider-component>)"
    },
    {
      "type": "liquid",
      "id": "custom_liquid_after",
      "label": "Custom liquid after (loads after <slider-component>)"
    }
  ],
  "blocks": [{ "type": "_column" }],
  "presets": [
    {
      "name": "Supercolumn",
      "settings": {
        "columns_mobile": "1",
        "columns_desktop": 2
      },
      "blocks": [
        { "type": "_column" },
        { "type": "_column" }
      ]
    }
  ]
}
{% endschema %}
