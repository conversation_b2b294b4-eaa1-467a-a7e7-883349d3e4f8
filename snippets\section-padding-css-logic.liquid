{% assign tag_scope_name = tag_name | default: 'section' %}
.{{ tag_scope_name }}-{{ section.id }}-padding {
  padding-top: {{ section.settings.padding_top_mobile | default: '0' }}px;
  padding-bottom: {{ section.settings.padding_bottom_mobile | default: '0' }}px;
  {% if section.settings.padding_left_mobile and section.settings.padding_right_mobile %}
    padding-left: {{ section.settings.padding_left_mobile }}px;
    padding-right: {{ section.settings.padding_right_mobile }}px;
  {% endif %}
}
@media screen and (min-width: 750px) {
.{{ tag_scope_name }}-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | default: section.settings.padding_top_desktop | default: '0' }}px;
    padding-bottom: {{ section.settings.padding_bottom | default: section.settings.padding_bottom_desktop | default: '0' }}px;
    {% if section.settings.padding_left_desktop and section.settings.padding_right_desktop %}
      padding-left: {{ section.settings.padding_left_desktop }}px;
      padding-right: {{ section.settings.padding_right_desktop }}px;
    {% endif %}
  }
}