{% comment %}
  Renders a megamenu for the header.
  bon-add: support for single sub-menu dropdown

  Usage:
  {% render 'header-mega-menu' %}
{% endcomment %}

<nav class="header__inline-menu">
  <ul class="list-menu list-menu--inline" role="list">
    {%- for link in section.settings.menu.links -%}
      <li>
        {%- capture menu_blocks -%}
          {% for block in section.blocks %}
            {%- if link.title == block.settings.title_match %}
              <li class="grid__item grid__item--span-{{ block.settings.colomn_span_amount_desktop }}-desktop">
                {%- case block.type -%}
                  {%- when 'custom_liquid' -%}
                    {{ block.settings.custom_liquid }}
                  {%- when 'image' -%}
                    {% render 'card-image-link',
                      link_url: block.settings.link,
                      link_label: block.settings.link_label,
                      media_image: block.settings.image,
                      media_aspect_ratio: section.settings.image_ratio,
                      columns: 2,
                      extend_height: true,
                      wrapper_class: 'product-card-wrapper'
                    %}
                  {%- when 'product' -%}
                    {% render 'card-product',
                      card_product: block.settings.product,
                      media_aspect_ratio: section.settings.image_ratio,
                      show_secondary_image: block.settings.second_image,
                      extend_height: true,
                      show_alternative_title: block.settings.show_alternative_title
                    %}
                  {%- when 'collection' -%}
                    {% render 'card-collection',
                      card_collection: block.settings.collection,
                      media_aspect_ratio: section.settings.image_ratio,
                      columns: 2,
                      extend_height: true,
                      wrapper_class: 'product-card-wrapper'
                    %}
                {%- endcase -%}
              </li>
            {%- endif -%}
          {% endfor %}
        {%- endcapture -%}

        {%- if link.links != blank or menu_blocks != blank -%}
          {%- liquid
            assign sub_menu_columns = ''
            assign sub_menu_blocks = section.blocks | where: 'type', 'sub_menu_columns'
            for block in sub_menu_blocks
              if link.title == block.settings.title_match
                assign sub_menu_columns = block.settings.colomn_span_amount_desktop
              endif
            endfor 
          -%}

          {%- liquid
            # bon-add: support for single sub-men dropdown
            assign single_submenu = false
            if link.levels == 1 and sub_menu_columns == blank
              assign single_submenu = true
            endif
          -%}

          <header-menu>
            <details id="Details-HeaderMenu-{{ forloop.index }}" class="{% if single_submenu %}dropdown-menu{% else %}mega-menu{% endif %}">
              <summary
                id="HeaderMenu-{{ link.handle }}"
                class="header__menu-item list-menu__item link focus-inset"
              >
                <span
                  {%- if link.child_active %}
                    class="header__active-menu-item"
                  {% endif %}
                >
                  {{- link.title | escape -}}
                </span>
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </summary>
              {% comment %} bon-add: support for single sub-men dropdown {% endcomment %}
              {%- if single_submenu -%}
                <ul
                  id="HeaderMenu-MenuList-{{ forloop.index }}"
                  class="header__submenu list-menu list-menu--disclosure color-{{ section.settings.menu_color_scheme }} gradient caption-large motion-reduce global-settings-popup"
                  role="list"
                  tabindex="-1"
                >
                  {%- for childlink in link.links -%}
                    <li>
                      <a
                        id="HeaderMenu-{{ link.handle }}-{{ childlink.handle }}"
                        href="{{ childlink.url }}"
                        class="header__menu-item list-menu__item link link--text focus-inset caption-large{% if childlink.current %} list-menu__item--active{% endif %}"
                        {% if childlink.current %}
                          aria-current="page"
                        {% endif %}
                      >
                        {{ childlink.title | escape }}
                      </a>
                      {%- if childlink.links != blank -%}
                        <ul class="list-unstyled" role="list">
                          {%- for grandchildlink in childlink.links -%}
                            <li>
                              <a
                                id="HeaderMenu-{{ link.handle }}-{{ childlink.handle }}-{{ grandchildlink.handle }}"
                                href="{{ grandchildlink.url }}"
                                class="header__menu-item link link--text list-menu__item focus-inset caption-large{% if grandchildlink.current %} list-menu__item--active{% endif %}"
                                {% if grandchildlink.current %}
                                  aria-current="page"
                                {% endif %}
                              >
                                {{ grandchildlink.title | escape }}
                              </a>
                            </li>
                          {%- endfor -%}
                        </ul>
                      {%- endif -%}
                    </li>
                  {%- endfor -%}
                </ul>
              {%- else -%}
                <div
                  id="MegaMenu-Content-{{ forloop.index }}"
                  class="mega-menu__content color-{{ section.settings.menu_color_scheme }} gradient motion-reduce global-settings-popup"
                  tabindex="-1"
                >
                  <div class="page-width">
                    <ul
                      class="mega-menu__list{% if link.levels == 1 and menu_blocks == blank %} mega-menu__list--condensed{% endif %} grid grid--{{ sub_menu_columns | default: section.settings.columns_desktop }}-col-desktop"
                      role="list"
                    >
                      {%- for childlink in link.links -%}
                        <li class="grid__item grid__item--span-{{ block.settings.colomn_span_amount_desktop }}-desktop">
                          <a
                            id="HeaderMenu-{{ link.handle }}-{{ childlink.handle }}"
                            href="{{ childlink.url }}"
                            class="mega-menu__link mega-menu__link--level-2 link{% if childlink.current %} mega-menu__link--active{% endif %}"
                            {% if childlink.current %}
                              aria-current="page"
                            {% endif %}
                          >
                            {{ childlink.title | escape }}
                          </a>
                          {%- if childlink.links != blank -%}
                            <ul class="list-unstyled" role="list">
                              {%- for grandchildlink in childlink.links -%}
                                <li>
                                  <a
                                    id="HeaderMenu-{{ link.handle }}-{{ childlink.handle }}-{{ grandchildlink.handle }}"
                                    href="{{ grandchildlink.url }}"
                                    class="mega-menu__link link{% if grandchildlink.current %} mega-menu__link--active{% endif %}"
                                    {% if grandchildlink.current %}
                                      aria-current="page"
                                    {% endif %}
                                  >
                                    {{ grandchildlink.title | escape }}
                                  </a>
                                </li>
                              {%- endfor -%}
                            </ul>
                          {%- endif -%}
                        </li>
                      {%- endfor -%}

                      {{ menu_blocks }}
                    </ul>
                  </div>
                </div>
              {%- endif -%}
            </details>
          </header-menu>
        {%- else -%}
          <a
            id="HeaderMenu-{{ link.handle }}"
            href="{{ link.url }}"
            class="header__menu-item list-menu__item link link--text focus-inset"
            {% if link.current %}
              aria-current="page"
            {% endif %}
          >
            <span
              {%- if link.current %}
                class="header__active-menu-item"
              {% endif %}
            >
              {{- link.title | escape -}}
            </span>
          </a>
        {%- endif -%}
      </li>
    {%- endfor -%}
  </ul>
</nav>