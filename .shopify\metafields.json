{"article": [], "blog": [], "collection": [{"key": "hidden", "namespace": "seo", "name": "Search Visibility", "description": "Set to 1 to hide from search", "type": {"name": "number_integer", "category": "NUMBER"}}], "company": [], "company_location": [], "location": [], "market": [], "order": [], "page": [{"key": "subtitle", "namespace": "custom", "name": "Subtitle", "description": "Provides subtitle for page", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "terpene", "namespace": "custom", "name": "Terpene", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "terpene_link_1", "namespace": "custom", "name": "Terpene Link 1", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "terpene_link_2", "namespace": "custom", "name": "Terpene Link 2", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}, {"key": "terpene_link_3", "namespace": "custom", "name": "Terpene Link 3", "description": "", "type": {"name": "metaobject_reference", "category": "REFERENCE"}}], "product": [{"key": "custom_product", "namespace": "mm-google-shopping", "name": "Google: Custom Product", "description": "Use to indicate whether or not the unique product identifiers (UPIs) GTIN, MPN, and brand are available for your product.", "type": {"name": "boolean", "category": "TRUE_FALSE"}}, {"key": "product_variants", "namespace": "bonshore", "name": "Product Variants", "description": "Fake variants allow you to link multiple products together to simulate regular product variants.", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "product_variants_name", "namespace": "bonshore", "name": "Product Variants Name", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "nutrition_facts_page", "namespace": "custom", "name": "Nutrition Facts Page", "description": "", "type": {"name": "page_reference", "category": "REFERENCE"}}, {"key": "drink_can_rear_image", "namespace": "custom", "name": "Drink Can Rear Image", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "dietary-preferences", "namespace": "shopify", "name": "Dietary preferences", "description": "Helps identify products suitable for specific diets, e.g. vegan, nut free", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "hidden", "namespace": "seo", "name": "Search visibility", "description": "", "type": {"name": "number_integer", "category": "NUMBER"}}, {"key": "flavor_color", "namespace": "custom", "name": "Flavor color", "description": "", "type": {"name": "color", "category": "COLOR"}}, {"key": "linked_products", "namespace": "custom", "name": "Linked products", "description": "", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "linked_products_name", "namespace": "custom", "name": "Linked products name", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "flavor_information", "namespace": "custom", "name": "Flavor information", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "fabric", "namespace": "shopify", "name": "<PERSON><PERSON><PERSON>", "description": "Identifies the type of fabric used in a product, for example, cotton or denim", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "color-pattern", "namespace": "shopify", "name": "Color", "description": "Defines the primary color or pattern, such as blue or striped", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "flavor", "namespace": "shopify", "name": "Flavor", "description": "Specifies the taste or flavor of a product, e.g. vanilla, mint", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "cbd-ingredients", "namespace": "shopify", "name": "CBD ingredients", "description": "Identifies the specific CBD-related components in a product, like full-spectrum CBD or sweeteners", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "age-group", "namespace": "shopify", "name": "Age group", "description": "Defines the target age range for a product, such as adults or kids", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "ways_to_buy", "namespace": "custom", "name": "Ways to buy", "description": "", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "dietary-supplements", "namespace": "shopify", "name": "Dietary supplements", "description": "Identifies the added nutrients in a product, like calcium or probiotics", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}], "variant": [{"key": "custom_label_0", "namespace": "mm-google-shopping", "name": "Google: Custom Label 0", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_2", "namespace": "mm-google-shopping", "name": "Google: Custom Label 02", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_3", "namespace": "mm-google-shopping", "name": "Google: Custom Label 3", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "gender", "namespace": "mm-google-shopping", "name": "Google: Gender", "description": "The gender for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "mpn", "namespace": "mm-google-shopping", "name": "Google: MPN", "description": "Your product’s Manufacturer Part Number (MPN).", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_4", "namespace": "mm-google-shopping", "name": "Google: Custom Label 4", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "age_group", "namespace": "mm-google-shopping", "name": "Google: Age Group", "description": "The demographic for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_system", "namespace": "mm-google-shopping", "name": "Google: Size System", "description": "The country of the size system used by your product.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_type", "namespace": "mm-google-shopping", "name": "Google: Size Type", "description": "Your apparel product’s cut.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "condition", "namespace": "mm-google-shopping", "name": "Google: Condition", "description": "The condition of your product at time of sale.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_1", "namespace": "mm-google-shopping", "name": "Google: Custom Label 1", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns.", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "shop": [{"key": "subMap", "namespace": "SavedBy", "name": "SavedBy - Sub Map", "description": null, "type": {"name": "json", "category": "JSON"}}, {"key": "protectionProduct", "namespace": "SavedBy", "name": "SavedBy - Protection Product", "description": null, "type": {"name": "product_reference", "category": "REFERENCE"}}, {"key": "feeTiers", "namespace": "SavedBy", "name": "SavedBy - <PERSON><PERSON>", "description": null, "type": {"name": "json", "category": "JSON"}}, {"key": "status", "namespace": "SavedBy", "name": "SavedBy - Status", "description": null, "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "nonCoveredProducts", "namespace": "SavedBy", "name": "SavedBy - Non-Covered Products", "description": null, "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "excludedProducts", "namespace": "SavedBy", "name": "SavedBy - Excluded Products", "description": null, "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "sfwVersion", "namespace": "SavedBy", "name": "SavedBy - SFW Version", "description": null, "type": {"name": "single_line_text_field", "category": "TEXT"}}]}