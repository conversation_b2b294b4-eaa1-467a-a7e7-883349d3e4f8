{% comment %}
  bs-add
  - ability to hide/show section
  - ability to have separate padding for desktop/mobile
  - new color options and also gradient flag for colors
  - ability to override text with liquid
  - new liquid block
  - more layout options, etc.
  - mobile image support
  - support for popup block
  - support for image contain option
{% endcomment %}

{{ 'component-image-with-text.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}

  #shopify-section-{{ section.id }} .image-with-text__content {
    padding: 4rem {{ section.settings.content_padding_mobile }}rem 5rem;
  }

  @media screen and (min-width: 750px) {
    #shopify-section-{{ section.id }} .image-with-text__content {
      padding: 4rem {{ section.settings.content_padding_desktop }}rem 5rem;
    }
  }

  {% if section.settings.image_mobile_contain %}
    .section-{{ section.id }}-padding .media > img,
    .section-{{ section.id }}-padding .media > video {
      object-fit: contain;
    }
  {% endif %}

  {% if section.settings.image_contain %}
    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding .media > img,
      .section-{{ section.id }}-padding .media > video {
        object-fit: contain;
      }
    }
  {% endif %}
{%- endstyle -%}

{%- liquid
  assign fetch_priority = 'auto'
  if section.index == 1
    assign fetch_priority = 'high'
  endif
  if section.settings.color_scheme_content == section.settings.color_scheme_section and section.settings.content_layout == 'no-overlap'
    assign remove_color_classes = true
  endif
-%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="color-{{ section.settings.color_scheme_section }} gradient">
    <div class="image-with-text image-with-text--{{ section.settings.content_layout }} {{ section.settings.content_width }} isolate{% if settings.text_boxes_border_thickness > 0 and settings.text_boxes_border_opacity > 0 and settings.media_border_thickness > 0 and settings.media_border_opacity > 0 %} collapse-borders{% endif %}{% unless section.settings.color_scheme_content == section.settings.section_color_scheme and settings.media_border_thickness > 0 and settings.text_boxes_shadow_opacity == 0 and settings.text_boxes_border_thickness == 0 or settings.text_boxes_border_opacity == 0 %} collapse-corners{% endunless %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
      <div class="{{ section.settings.content_position }}">
        <div class="section-{{ section.id }}-padding">
          <div class="image-with-text__grid grid grid--gapless grid--1-col grid--{% if section.settings.desktop_image_width == 'medium' %}2-col-tablet{% else %}3-col-tablet{% endif %}{% if section.settings.layout == 'text_first' %} image-with-text__grid--reverse{% endif %}">
            {% assign mobile_image_block = section.blocks | where: 'type', 'image_placement_mobile' %}
            <div class="image-with-text__media-item image-with-text__media-item--{{ section.settings.desktop_image_width }} image-with-text__media-item--{{ section.settings.desktop_content_position }} grid__item {% if mobile_image_block.size == 1 %}small-hide{% endif %}">
              {% comment %}
                bs-add
                - move size and widths here for reuse
                - temp fix for mobile resolution, divide by 1 instead of 2 for higher-res images
              {% endcomment %}
              {%- if section.settings.image_behavior == 'ambient' or section.settings.image_behavior == 'zoom-in' -%}
                {%- assign widths = '198, 432, 642, 900, 1284, 1800' -%}
                {%- capture sizes -%}
                  (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 1.6667 }}px,
                  (min-width: 750px) calc((100vw - 130px) / 1.667), calc((100vw - 50px) / 1.667)
                {%- endcapture -%}
              {%- else -%}
                {%- assign widths = '165, 360, 535, 750, 1070, 1500' -%}
                {%- capture sizes -%}
                  (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
                  (min-width: 750px) calc((100vw - 130px) / 1), calc((100vw - 50px) / 1)
                {%- endcapture -%}
              {%- endif -%}

              {% capture image_content %}
                {% liquid 
                  if section.settings.video != blank
                    assign aspect_ratio = section.settings.video.preview_image.aspect_ratio
                  else
                    assign aspect_ratio = section.settings.image.aspect_ratio
                  endif

                  assign has_media_mobile = false
                  if section.settings.image_mobile != blank or section.settings.video_mobile != blank
                    assign has_media_mobile = true
                  endif
                %}
                <div 
                  class="image-with-text__media image-with-text__media--{{ section.settings.height }}{% unless remove_color_classes %} gradient color-{{ section.settings.color_scheme_content }}{% else %} background-transparent{% endunless %} {% if section.settings.image != blank or section.settings.video != blank %}media{% else %}image-with-text__media--placeholder placeholder{% endif %}{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %} {% if has_media_mobile %}small-hide{% endif %}"
                  {% if section.settings.height == 'adapt' and section.settings.image != blank %} 
                    style="padding-bottom: {{ 1 | divided_by: aspect_ratio | times: 100 }}%;"
                  {% endif %}
                >
                  {%- if section.settings.video != blank -%}
                    {{
                      section.settings.video
                      | video_tag: image_size: '1500x', loop: true, controls: section.settings.show_video_controls, muted: true, autoplay: true
                    }}
                  {%- elsif section.settings.image != blank -%}
                    {%- liquid 
                      assign fetch_priority_desktop = fetch_priority
                      if section.settings.image_mobile != blank
                        assign fetch_priority_desktop = 'auto'
                      endif 
                    -%}
                    {{
                      section.settings.image
                      | image_url: width: 1500
                      | image_tag: sizes: sizes, widths: widths, fetchpriority: fetch_priority_desktop
                    }}
                  {%- else -%}
                    {{ 'detailed-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- endif -%}
                </div>

                {%- if has_media_mobile -%}
                  {%- liquid
                    if section.settings.video_mobile != blank
                      assign aspect_ratio_mobile = section.settings.video_mobile.preview_image.aspect_ratio
                    else
                      assign aspect_ratio_mobile = section.settings.image_mobile.aspect_ratio
                    endif
                  -%}
                  <div 
                    class="image-with-text__media image-with-text__media--{{ section.settings.height_mobile }} global-media-settings{% unless remove_color_classes %} gradient color-{{ section.settings.color_scheme_content }}{% else %} background-transparent{% endunless %} media{% if section.settings.image_behavior != 'none' %} animate--{{ section.settings.image_behavior }}{% endif %} medium-hide large-up-hide"
                    {% if section.settings.height_mobile == 'adapt' %} 
                      style="padding-bottom: {{ 1 | divided_by: aspect_ratio_mobile | times: 100 }}%;"
                    {% endif %}
                  >
                    {%- if section.settings.video_mobile != blank -%}
                      {{
                        section.settings.video_mobile
                        | video_tag: image_size: '1500x', loop: true, controls: section.settings.show_video_mobile_controls, muted: true, autoplay: true
                      }}
                    {%- else -%}
                      {{
                        section.settings.image_mobile
                        | image_url: width: 1500
                        | image_tag: sizes: sizes, widths: widths, fetchpriority: fetch_priority
                      }}
                    {%- endif -%}
                  </div>
                {%- endif -%}
              {% endcapture %}
              {{ image_content }}
            </div>
            <div class="image-with-text__text-item grid__item">
              <div
                id="ImageWithText--{{ section.id }}"
                class="image-with-text__content image-with-text__content--{{ section.settings.desktop_content_position }} image-with-text__content--desktop-{{ section.settings.desktop_content_alignment }} image-with-text__content--mobile-{{ section.settings.mobile_content_alignment }} image-with-text__content--{{ section.settings.height }}{% unless remove_color_classes %} gradient color-{{ section.settings.color_scheme_content }}{% else %} background-transparent{% endunless %} content-container"
              >
                {%- for block in section.blocks -%}
                  {% case block.type %}
                    {%- when '@app' -%}
                      {% render block %}
                    {%- when 'custom_liquid' -%}
                      {{ block.settings.custom_liquid }}
                    {%- when 'image_placement_mobile' -%}
                      <div class="image-with-text__media-item image-with-text__media-item--{{ section.settings.desktop_image_width }} image-with-text__media-item--{{ section.settings.desktop_content_position }} grid__item medium-hide large-up-hide">
                        {{ image_content }}
                      </div>
                    {%- when 'heading' -%}
                      <h2
                        class="image-with-text__heading inline-richtext {{ block.settings.heading_size }}"
                        {{ block.shopify_attributes }}
                      >
                        {{ block.settings.heading_liquid | default: block.settings.heading }}
                      </h2>
                    {%- when 'caption' -%}
                      <p
                        class="image-with-text__text image-with-text__text--caption rte {{ block.settings.text_style }} {{ block.settings.text_style }}--{{ block.settings.text_size }} {{ block.settings.text_style }}"
                        {{ block.shopify_attributes }}
                      >
                        {{ block.settings.caption_liquid | default: block.settings.caption }}
                      </p>
                    {%- when 'text' -%}
                      <div
                        class="image-with-text__text rte {{ block.settings.text_style }}"
                        {{ block.shopify_attributes }}
                      >
                        {{ block.settings.text }}
                      </div>
                    {%- when 'button' -%}
                      {%- assign button_label_content = block.settings.button_label_liquid
                        | default: block.settings.button_label
                      -%}
                      {%- assign button_label_2_content = block.settings.button_label_2_liquid
                        | default: block.settings.button_label_2
                      -%}
                      <div
                        class="image-with-text__buttons{% if button_label_content != blank and button_label_2_content != blank %} image-with-text__buttons--multiple{% endif %}"
                        {{ block.shopify_attributes }}
                      >
                        {%- if button_label_content != blank -%}
                          <a
                            {% if block.settings.button_link == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block.settings.button_link }}"
                            {% endif %}
                            class="{{ block.settings.button_style }}"
                          >
                            {{ button_label_content }}
                          </a>
                        {%- endif -%}

                        {%- if button_label_2_content != blank -%}
                          <a
                            {% if block.settings.button_link_2 == blank %}
                              role="link" aria-disabled="true"
                            {% else %}
                              href="{{ block.settings.button_link_2 }}"
                            {% endif %}
                            class="{{ block.settings.button_style_2 }}"
                          >
                            {{ button_label_2_content }}
                          </a>
                        {%- endif -%}
                      </div>

                      {%- comment -%}
                        bs-add
                          - support for popups
                          - outside div needed for alignment
                      {%- endcomment -%}
                    {%- when 'popup' -%}
                      <div>
                        {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
                        <modal-opener
                          class="product-popup-modal__opener quick-add-hidden"
                          data-modal="#PopupModal-{{ block.id }}"
                          {{ block.shopify_attributes }}
                        >
                          <button
                            id="ProductPopup-{{ block.id }}"
                            class="{% if block.settings.button_style contains 'link' %}product-popup-modal__button{% endif %} {{ block.settings.button_style }}"
                            style="{% if block.settings.button_style contains 'link' %}min-height: auto;padding-right: 0;text-decoration: underline;{% endif %}"
                            type="button"
                            aria-haspopup="dialog"
                          >
                            {{ block.settings.text_liquid | default: block.settings.text }}
                          </button>
                        </modal-opener>
                      </div>
                  {%- endcase -%}
                {%- endfor -%}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{%- comment -%}
  bs-add
    - support for popups
{%- endcomment -%}
{% assign popups = section.blocks | where: 'type', 'popup' %}
{%- for block in popups -%}
  <modal-dialog
    id="PopupModal-{{ block.id }}"
    class="product-popup-modal color-{{ block.settings.color_scheme }} gradient"
    {{ block.shopify_attributes }}
  >
    <div
      role="dialog"
      aria-label="{{ block.settings.text_liquid | default: block.settings.text | strip_html | strip }}"
      aria-modal="true"
      class="product-popup-modal__content"
      tabindex="-1"
    >
      <button
        id="ModalClose-{{ block.id }}"
        type="button"
        class="product-popup-modal__toggle"
        aria-label="{{ 'accessibility.close' | t }}"
      >
        {{- 'icon-close.svg' | inline_asset_content -}}
      </button>
      <div class="product-popup-modal__content-info">
        <h3 class="h2">{{ block.settings.title | default: block.settings.page.title }}</h3>
        {{ block.settings.content_liquid | default: block.settings.page.content }}
      </div>
    </div>
  </modal-dialog>
{%- endfor -%}

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.image-with-text.name",
  "class": "section section--image-with-text",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.image-with-text.settings.image.label"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:sections.video.settings.video.label"
    },
    {
      "type": "checkbox",
      "id": "show_video_controls",
      "label": "Show video controls",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "image_contain",
      "label": "Contain image",
      "default": false
    },
    {
      "type": "select",
      "id": "height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-with-text.settings.height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-with-text.settings.height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.height.options__4.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.image-with-text.settings.height.label"
    },
    {
      "type": "image_picker",
      "id": "image_mobile",
      "label": "Mobile image"
    },
    {
      "type": "video",
      "id": "video_mobile",
      "label": "Mobile video"
    },
    {
      "type": "checkbox",
      "id": "show_video_mobile_controls",
      "label": "Show mobile video controls",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "image_mobile_contain",
      "label": "Contain mobile image",
      "default": false
    },
    {
      "type": "select",
      "id": "height_mobile",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.image-with-text.settings.height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-with-text.settings.height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.height.options__4.label"
        }
      ],
      "default": "adapt",
      "label": "Mobile Image height"
    },
    {
      "type": "select",
      "id": "desktop_image_width",
      "options": [
        {
          "value": "small",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.image-with-text.settings.desktop_image_width.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.image-with-text.settings.desktop_image_width.label",
      "info": "t:sections.image-with-text.settings.desktop_image_width.info"
    },
    {
      "type": "select",
      "id": "layout",
      "options": [
        {
          "value": "image_first",
          "label": "t:sections.image-with-text.settings.layout.options__1.label"
        },
        {
          "value": "text_first",
          "label": "t:sections.image-with-text.settings.layout.options__2.label"
        }
      ],
      "default": "image_first",
      "label": "t:sections.image-with-text.settings.layout.label",
      "info": "t:sections.image-with-text.settings.layout.info"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "middle",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "bottom",
          "label": "t:sections.image-with-text.settings.desktop_content_position.options__3.label"
        }
      ],
      "default": "middle",
      "label": "t:sections.image-with-text.settings.desktop_content_position.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-with-text.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.image-with-text.settings.desktop_content_alignment.label"
    },
    {
      "type": "select",
      "id": "content_layout",
      "options": [
        {
          "value": "no-overlap",
          "label": "t:sections.image-with-text.settings.content_layout.options__1.label"
        },
        {
          "value": "overlap",
          "label": "t:sections.image-with-text.settings.content_layout.options__2.label"
        }
      ],
      "default": "no-overlap",
      "label": "t:sections.image-with-text.settings.content_layout.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_content",
      "label": "Content color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.animation.content"
    },
    {
      "type": "select",
      "id": "image_behavior",
      "options": [
        {
          "value": "none",
          "label": "t:sections.all.animation.image_behavior.options__1.label"
        },
        {
          "value": "ambient",
          "label": "t:sections.all.animation.image_behavior.options__2.label"
        },
        {
          "value": "zoom-in",
          "label": "t:sections.all.animation.image_behavior.options__4.label"
        }
      ],
      "default": "none",
      "label": "t:sections.all.animation.image_behavior.label"
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.image-with-text.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.image-with-text.settings.mobile_content_alignment.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 300,
      "step": 10,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "page",
      "id": "page",
      "label": "Linked Page",
      "info": "Allows page linking to access metafields in unique cases (e.g. home page)."
    },
    {
      "type": "header",
      "content": "Advanced: Visibility"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Advanced: Section Colors"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_section",
      "label": "Background color scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Advanced: Layout"
    },
    {
      "type": "select",
      "id": "content_width",
      "options": [
        {
          "value": "full-width",
          "label": "Full width on all devices"
        },
        {
          "value": "page-width",
          "label": "Page width (based on theme settings)"
        },
        {
          "value": "page-width page-width--narrow",
          "label": "Page width narrow (tablet and desktop only)"
        }
      ],
      "default": "page-width",
      "label": "Section width"
    },
    {
      "type": "select",
      "id": "content_position",
      "options": [
        {
          "value": "full-width",
          "label": "Full width"
        },
        {
          "value": "left-one-third",
          "label": "Left one third"
        },
        {
          "value": "left-one-half",
          "label": "Left one half"
        },
        {
          "value": "left-two-thirds",
          "label": "Left two thirds"
        },
        {
          "value": "right-one-third",
          "label": "Right one third"
        },
        {
          "value": "right-one-half",
          "label": "Right one half"
        },
        {
          "value": "right-two-thirds",
          "label": "Right two thirds"
        }
      ],
      "default": "full-width",
      "label": "Content position within section",
      "info": "Only applies to desktop (min-width: 990px)"
    },
    {
      "type": "range",
      "id": "padding_left_mobile",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Padding left mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_mobile",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Padding right mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_left_desktop",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Padding left desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_desktop",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Padding right desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced: Block layout settings"
    },
    {
      "type": "range",
      "id": "content_padding_mobile",
      "min": 0.5,
      "max": 10,
      "step": 0.5,
      "unit": "rem",
      "label": "Block horizontal padding mobile (max-width: 749px)",
      "info": "Top is set to 4rem | Bottom is set to 5rem",
      "default": 1.5
    },
    {
      "type": "range",
      "id": "content_padding_desktop",
      "min": 0.5,
      "max": 10,
      "step": 0.5,
      "unit": "rem",
      "label": "Block horizontal padding desktop (min-width: 750px)",
      "info": "Top is set to 4rem | Bottom is set to 5rem",
      "default": 4
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "heading",
      "name": "t:sections.image-with-text.blocks.heading.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.image-with-text.blocks.heading.settings.heading.default",
          "label": "t:sections.image-with-text.blocks.heading.settings.heading.label"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            },
            {
              "value": "hxl",
              "label": "t:sections.all.heading_size.options__4.label"
            },
            {
              "value": "hxxl",
              "label": "t:sections.all.heading_size.options__5.label"
            }
          ],
          "default": "h2",
          "label": "Heading size"
        }
      ]
    },
    {
      "type": "caption",
      "name": "t:sections.image-with-text.blocks.caption.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "caption",
          "default": "t:sections.image-with-text.blocks.caption.settings.text.default",
          "label": "t:sections.image-with-text.blocks.caption.settings.text.label"
        },
        {
          "type": "liquid",
          "id": "caption_liquid",
          "label": "Text liquid",
          "info": "Overrides Text above"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__1.label"
            },
            {
              "value": "caption-with-letter-spacing",
              "label": "t:sections.image-with-text.blocks.caption.settings.text_style.options__2.label"
            }
          ],
          "default": "caption-with-letter-spacing",
          "label": "t:sections.image-with-text.blocks.caption.settings.text_style.label"
        },
        {
          "type": "select",
          "id": "text_size",
          "options": [
            {
              "value": "small",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__1.label"
            },
            {
              "value": "medium",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__2.label"
            },
            {
              "value": "large",
              "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.options__3.label"
            }
          ],
          "default": "medium",
          "label": "t:sections.image-with-text.blocks.caption.settings.caption_size.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.image-with-text.blocks.text.name",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "t:sections.image-with-text.blocks.text.settings.text.default",
          "label": "t:sections.image-with-text.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.image-with-text.blocks.text.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.image-with-text.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "button",
      "name": "t:sections.image-with-text.blocks.button.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "button_label",
          "default": "t:sections.image-with-text.blocks.button.settings.button_label.default",
          "label": "Button label",
          "info": "Leave the label blank to hide the button."
        },
        {
          "type": "liquid",
          "id": "button_label_liquid",
          "label": "Button label liquid",
          "info": "Overrides Button label above"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.image-with-text.blocks.button.settings.button_link.label"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--primary",
          "label": "Button style"
        },
        {
          "type": "inline_richtext",
          "id": "button_label_2",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_label_2.label",
          "info": "t:sections.image-banner.blocks.buttons.settings.button_label_2.info"
        },
        {
          "type": "liquid",
          "id": "button_label_2_liquid",
          "label": "Second button label liquid",
          "info": "Overrides Second button label above"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "t:sections.image-banner.blocks.buttons.settings.button_link_2.label"
        },
        {
          "type": "select",
          "id": "button_style_2",
          "options": [
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "button button--secondary",
          "label": "Second button style"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        }
      ]
    },
    {
      "type": "image_placement_mobile",
      "name": "Mobile image placement",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "This block overrides main section image placement on mobile. Remove block to revert to default placement."
        }
      ]
    },
    {
      "type": "popup",
      "name": "t:sections.main-product.blocks.popup.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "Pop-up link text",
          "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
        },
        {
          "type": "liquid",
          "id": "text_liquid",
          "label": "Link label liquid",
          "info": "Overrides Link label above"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "Title",
          "info": "Overrides page title if set"
        },
        {
          "id": "page",
          "type": "page",
          "label": "t:sections.main-product.blocks.popup.settings.page.label"
        },
        {
          "type": "liquid",
          "id": "content_liquid",
          "label": "Custom content",
          "info": "Overrides page setting above"
        },
        {
          "type": "select",
          "id": "button_style",
          "options": [
            {
              "value": "link underlined-link",
              "label": "Link"
            },
            {
              "value": "button button--primary",
              "label": "Solid"
            },
            {
              "value": "button button--secondary",
              "label": "Outline"
            },
            {
              "value": "button button-1",
              "label": "Custom 1"
            },
            {
              "value": "button button-2",
              "label": "Custom 2"
            },
            {
              "value": "button button-3",
              "label": "Custom 3"
            },
            {
              "value": "button button-4",
              "label": "Custom 4"
            },
            {
              "value": "button button-5",
              "label": "Custom 5"
            }
          ],
          "default": "link underlined-link",
          "label": "Button style"
        },
        {
          "type": "color_scheme",
          "id": "color_scheme",
          "default": "scheme-1",
          "label": "t:sections.all.colors.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.image-with-text.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}
