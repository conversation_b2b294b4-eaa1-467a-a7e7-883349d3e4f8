.cart {
  position: relative;
  display: block;
}

.cart__empty-text,
.is-empty .cart__contents,
cart-items.is-empty .title-wrapper-with-link,
.is-empty .cart__footer {
  display: none;
}

.is-empty .cart__empty-text,
.is-empty .cart__warnings {
  display: block;
}

.cart__warnings {
  display: none;
  text-align: center;
  padding: 3rem 0 1rem;
}

.cart__empty-text {
  margin: 4.5rem 0 2rem;
}

.cart__contents > * + * {
  margin-top: 2.5rem;
}

.cart__login-title {
  margin: 5.5rem 0 0.5rem;
}

.cart__login-paragraph {
  margin-top: 0.8rem;
}

.cart__login-paragraph a {
  font-size: inherit;
}

@media screen and (min-width: 990px) {
  .cart__warnings {
    padding: 7rem 0 1rem;
  }

  .cart__empty-text {
    margin: 0 0 3rem;
  }
}

cart-items {
  display: block;
}

.cart__items {
  position: relative;
  padding-bottom: 3rem;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
}

.cart__items--disabled {
  pointer-events: none;
}

.cart__footer-wrapper:last-child .cart__footer {
  padding-bottom: 5rem;
}

.cart__footer > div:only-child {
  margin-left: auto;
}

.cart__footer > * + * {
  margin-top: 6.5rem;
}

.cart__footer .discounts {
  margin-bottom: 1rem;
}

.cart__note {
  height: fit-content;
  top: 2.5rem;
}

.cart__note label {
  display: flex;
  align-items: flex-end;
  position: absolute;
  line-height: 1;
  height: 1.8rem;
  top: -3rem;
  color: rgba(var(--color-foreground), 0.75);
}

.cart__note .field__input {
  height: 100%;
  position: relative;
  border-radius: var(--inputs-radius);
  padding: 1rem 2rem;
}

.cart__note .text-area {
  resize: vertical;
}

.cart__note:after,
.cart__note:hover.cart__note:after,
.cart__note:before,
.cart__note:hover.cart__note:before,
.cart__note .field__input:focus,
.cart__note .field__input {
  border-bottom-right-radius: 0;
}

@media screen and (min-width: 750px) {
  .cart__items {
    grid-column-start: 1;
    grid-column-end: 3;
    padding-bottom: 4rem;
  }

  .cart__contents > * + * {
    margin-top: 0;
  }

  .cart__items + .cart__footer {
    grid-column: 2;
  }

  .cart__footer {
    display: flex;
    justify-content: space-between;
    border: 0;
  }

  .cart__footer-wrapper:last-child {
    padding-top: 0;
  }

  .cart__footer > * {
    width: 35rem;
  }

  .cart__footer > * + * {
    margin-left: 4rem;
    margin-top: 0;
  }
}

.cart__ctas button {
  width: 100%;
}

.cart__ctas > * + * {
  margin-top: 1rem;
}

.cart__update-button {
  margin-bottom: 1rem;
}

.cart__dynamic-checkout-buttons {
  max-width: 36rem;
  margin: 0 auto;
}

.cart__dynamic-checkout-buttons:has(.dynamic-checkout__content:empty) {
  margin: 0;
}

.cart__blocks > * + * {
  margin-top: 1rem;
}

.cart-note__label {
  display: inline-block;
  margin-bottom: 1rem;
  line-height: calc(1 + 1 / var(--font-body-scale));
}

.tax-note {
  margin: 2.2rem 0 1.6rem auto;
  text-align: center;
  display: block;
}

.cart__checkout-button {
  max-width: 36rem;
}

.cart__ctas {
  text-align: center;
}

@media screen and (min-width: 750px) {
  .cart-note {
    max-width: 35rem;
  }

  .cart__update-button {
    /* bs-del
      - removed to keep margin on update button on desktop
    */
    margin-right: 0.8rem;
  }

  .tax-note {
    margin-bottom: 2.2rem;
    text-align: right;
  }

  [data-shopify-buttoncontainer] {
    justify-content: flex-end;
  }

  /* bs-del
    - removed for new button setup
  */
}
