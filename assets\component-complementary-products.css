.complementary-products__container {
  display: flex;
  flex-direction: column;
  gap: 1.3rem;
}

/* bs-add
  - support for x-sell
*/
.drawer__x-sell .complementary-products__container {
  gap: 0.5rem;
}

product-recommendations:not(.is-accordion) .complementary-products__container {
  margin-top: 1.5rem;
}

product-recommendations:not(.is-accordion)
  .complementary-products__container.complementary-products__container--padding {
  padding: calc(2rem / var(--font-body-scale));
}

/* bs-add
  - adjust top margin for first p tag
  - remove bottom margin for last p tag, using gap present from flexbox to control spacing
*/
product-recommendations:not(.is-accordion)
  .complementary-products__container
  .summary__title__description
  p:first-of-type {
  margin-top: .6rem;
}
product-recommendations:not(.is-accordion)
  .complementary-products__container
  .summary__title__description
  p:last-of-type {
  margin-bottom: 0;
}

.complementary-products__container > details[open] {
  padding-bottom: 1.5rem;
}

.complementary-slider {
  margin-top: 0;
  gap: 0;
}

.complementary-slide {
  --shadow-padding-sides: calc((var(--shadow-horizontal-offset) + var(--shadow-blur-radius)) * var(--shadow-visible));
  --shadow-padding-sides-negative: calc(
    (var(--shadow-horizontal-offset) * -1 + var(--shadow-blur-radius)) * var(--shadow-visible)
  );
}

.complementary-slide > ul {
  display: flex;
  flex-direction: column;
  gap: var(--grid-mobile-vertical-spacing);
}

/* bs-add
  - support for horizontal slider layout
*/
.complementary-slider.complementary-slider--horizontal .complementary-slide > ul {
  flex-direction: row;
  gap: var(--grid-mobile-horizontal-spacing);
}

.complementary-slide.complementary-slide--standard > ul {
  gap: calc(var(--grid-mobile-vertical-spacing) + 8px);
}

@media screen and (min-width: 750px) {
  .complementary-slide > ul {
    gap: var(--grid-desktop-vertical-spacing);
  }

  .complementary-slide.complementary-slide--standard > ul {
    gap: calc(var(--grid-desktop-vertical-spacing) + 8px);
  }
}

.complementary-slide.grid__item {
  width: 100%;
  padding-top: max(var(--focus-outline-padding), var(--shadow-padding-top));
  padding-bottom: max(var(--focus-outline-padding), var(--shadow-padding-bottom));
  padding-right: max(var(--focus-outline-padding), var(--shadow-padding-sides));
  padding-left: max(var(--focus-outline-padding), var(--shadow-padding-sides-negative));
}

/* bs-mod
  - remove padding if only slide
*/
.complementary-slide:only-child.grid__item {
  padding: 0;
}

/* bs-mod
  - fix for mobile layout not holding grid size (perhaps pull this into bass.css */
.complementary-slide .grid__item {
  flex-grow: 0;
}

.complementary-slide .card-wrapper {
  height: auto;
}

/* bs-add
  - support for horizontal slider layout (reset slide height to normal 100%)
*/
.complementary-slider.complementary-slider--horizontal .complementary-slide .card-wrapper {
  height: 100%;
}

.complementary-products > .summary__title {
  display: flex;
  line-height: 1;
  padding: 1.5rem 0;
}

.accordion + product-recommendations .accordion,
product-recommendations.is-accordion + .accordion {
  margin-top: 0;
  border-top: none;
}

.complementary-products > .summary__title .icon-accordion {
  fill: rgb(var(--color-foreground));
  height: calc(var(--font-heading-scale) * 2rem);
  margin-right: calc(var(--font-heading-scale) * 1rem);
  width: calc(var(--font-heading-scale) * 2rem);
}

.complementary-products__container .card--card .card__content,
.complementary-products__container .card--horizontal .card__information {
  padding: 0;
}

/* bs-add
  - support for x-sell
*/
.drawer__x-sell .complementary-products__container .card--horizontal .card__information {
  gap: 0;
}

.complementary-products__container .card--horizontal .card__inner {
  max-width: 20%;
}

/* bs-add
  - support for x-sell
*/
.drawer__x-sell .complementary-products__container .card--horizontal .card__inner {
  min-width: 20%;
  max-width: 25%;
}

@media screen and (min-width: 750px) and (max-width: 1200px) {
  .complementary-products__container .card--horizontal .card__inner {
    max-width: 25%;
  }
}

.complementary-slide .card--text .card__content {
  grid-template-rows: minmax(0, 1fr) max-content auto;
}

.complementary-products__container .card--card.card--media > .card__content {
  margin-top: 0;
}

.complementary-products-contains-quick-add .underline-links-hover:hover a {
  text-decoration: initial;
}

.complementary-products-contains-quick-add .card__heading:hover a {
  text-decoration: underline;
}

/* bs-add
  - support for x-sell
*/
.drawer__x-sell .card__heading .full-unstyled-link {
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
  text-overflow: ellipsis;
  max-width: 17rem;
}
@media screen and (min-width: 750px) {
  .drawer__x-sell .card__heading .full-unstyled-link {
    max-width: 21rem;
  }
}

.complementary-products__container .card--card .card__inner .card__media {
  border-radius: calc(var(--corner-radius) - var(--border-width) - var(--image-padding));
}

.complementary-products__container .card--horizontal .quick-add {
  margin: 0;
  max-width: 20rem;
}

/* bs-add
  - support for x-sell
*/
.drawer__x-sell .complementary-products__container .card--horizontal .quick-add {
  margin-top: 1rem;
}
.drawer__x-sell .complementary-products__container .card--horizontal .quick-add .quick-add__submit {
  padding: 0.8rem 1.3rem;
}

/* bs-mod
  - reduced padding to reduce competition with buy now button (matches pills)
  - support for quick add link
*/
.complementary-products__container .quick-add__submit,
.complementary-products__container .quick-add__link {
  padding: 1rem 2rem;
  min-height: inherit;
}

.complementary-products__container .quick-add__submit .icon-plus {
  width: 1.2rem;
}

.complementary-products__container .icon-wrap {
  display: flex;
}

.complementary-products .sold-out-message:not(.hidden) + .icon-wrap {
  display: none;
}

.complementary-products__container .quick-add__submit:not(.animate-arrow) .icon-wrap {
  transition: transform var(--duration-short) ease;
}

.complementary-products__container .quick-add__submit:not(.animate-arrow):hover .icon-wrap {
  transform: rotate(90deg);
}

/* bs-mod
  - support for quick add link
*/
.complementary-products__container .quick-add__link:after,
.complementary-products__container .quick-add__link:hover:after,
.complementary-products__container .quick-add__submit:after,
.complementary-products__container .quick-add__submit:hover:after {
  box-shadow: none;
}

.complementary-products__container .card--horizontal .quick-add,
.complementary-products__container .card__badge {
  justify-self: var(--text-alignment);
}

.product--no-media .complementary-products__container .price {
  text-align: var(--text-alignment);
}

@media screen and (min-width: 750px) {
  .complementary-products__container .price--on-sale .price-item--regular {
    font-size: 1.3rem;
  }
}

/* 
  bs-add
  - remove swatch verticle padding as not needed if positioned bottom
*/
.complementary-products__container .color-swatch__container.bottom {
  padding-top: 0;
}

/* bs-add
  - support for x-sell
*/
.drawer__x-sell .complementary-products__container .complementary-slide.grid__item {
  width: calc(100% - 5rem - var(--grid-mobile-horizontal-spacing));
}
.drawer__x-sell .complementary-products__container .grid--peek.slider .complementary-slide.grid__item:first-of-type {
  margin-left: -0.5rem;
}
@media screen and (min-width: 750px) {
  .drawer__x-sell .complementary-products__container .complementary-slide.grid__item {
    width: calc(100% - 5rem - var(--grid-desktop-horizontal-spacing));
  }
}