<div class="tw:h-full tw:w-full tw:flex tw:flex-col {{ block.settings.mobile_justify }} {{ block.settings.mobile_align }} {{ block.settings.mobile_text_align }} {{ block.settings.mobile_gap }} {{ block.settings.mobile_padding }} {{ block.settings.desktop_justify }} {{ block.settings.desktop_align }} {{ block.settings.desktop_text_align }} {{ block.settings.desktop_gap }} {{ block.settings.desktop_padding }} {{ block.settings.custom_classes }}">
  {% content_for 'blocks' %}
</div>

{% schema %}
{
  "name": "Flexbox",
  "tag": null,
  "blocks": [{ "type": "@theme" }, { "type": "@app" }],
  "settings": [
    {
      "type": "paragraph",
      "content": "This block renders a <div> with the following CSS classes: display: flex; flex-direction: column; width: 100%; height: 100%;"
    },
    {
      "type": "header",
      "content": "Mobile layout",
      "info": "Devices with screens < 750px"
    },
    {
      "type": "select",
      "id": "mobile_justify",
      "options": [
        {
          "value": "tw:justify-normal",
          "label": "Normal"
        },
        {
          "value": "tw:justify-stretch",
          "label": "Stretch"
        },
        {
          "value": "tw:justify-start",
          "label": "Start"
        },
        {
          "value": "tw:justify-center",
          "label": "Center"
        },
        {
          "value": "tw:justify-end",
          "label": "End"
        },
        {
          "value": "tw:justify-between",
          "label": "Space between"
        },
        {
          "value": "tw:justify-evenly",
          "label": "Space evenly"
        }
      ],
      "default": "tw:justify-start",
      "label": "Vertical",
      "info": "(CSS class: justify-content)"
    },
    {
      "type": "select",
      "id": "mobile_align",
      "options": [
        {
          "value": "tw:items-stretch",
          "label": "Stretch"
        },
        {
          "value": "tw:items-start",
          "label": "Left"
        },
        {
          "value": "tw:items-center",
          "label": "Center"
        },
        {
          "value": "tw:items-end",
          "label": "Right"
        }
      ],
      "default": "tw:items-start",
      "label": "Horizontal",
      "info": "(CSS class: align-items)"
    },
    {
      "type": "select",
      "id": "mobile_text_align",
      "options": [
        {
          "value": "tw:text-left",
          "label": "Left"
        },
        {
          "value": "tw:text-center",
          "label": "Center"
        },
        {
          "value": "tw:text-right",
          "label": "Right"
        },
      ],
      "default": "tw:text-left",
      "label": "Text alignmenet",
      "info": "When using blocks with text that wraps, you may need to align the text as well (CSS class: text-align)"

    },
    {
      "type": "select",
      "id": "mobile_gap",
      "options": [
        {
          "value": "tw:gap-0",
          "label": "0px"
        },
        {
          "value": "tw:gap-[4px]",
          "label": "4px"
        },
        {
          "value": "tw:gap-[8px]",
          "label": "8px"
        },
        {
          "value": "tw:gap-[16px]",
          "label": "16px"
        },
        {
          "value": "tw:gap-[24px]",
          "label": "24px"
        },
        {
          "value": "tw:gap-[32px]",
          "label": "32px"
        },
      ],
      "default": "tw:gap-[4px]",
      "label": "Gap"
    },
    {
      "type": "select",
      "id": "mobile_padding",
      "options": [
        {
          "value": "tw:p-0",
          "label": "0px"
        },
        {
          "value": "tw:p-[4px]",
          "label": "4px"
        },
        {
          "value": "tw:p-[8px]",
          "label": "8px"
        },
        {
          "value": "tw:p-[16px]",
          "label": "16px"
        },
        {
          "value": "tw:p-[24px]",
          "label": "24px"
        },
        {
          "value": "tw:p-[32px]",
          "label": "32px"
        },
      ],
      "default": "tw:p-[8px]",
      "label": "Padding"
    },
    {
      "type": "header",
      "content": "Desktop layout",
      "info": "Devices with screens >= 750px"
    },
    {
      "type": "select",
      "id": "desktop_justify",
      "options": [
        {
          "value": "tw:md:justify-normal",
          "label": "Normal"
        },
        {
          "value": "tw:md:justify-stretch",
          "label": "Stretch"
        },
        {
          "value": "tw:md:justify-start",
          "label": "Start"
        },
        {
          "value": "tw:md:justify-center",
          "label": "Center"
        },
        {
          "value": "tw:md:justify-end",
          "label": "End"
        },
        {
          "value": "tw:md:justify-between",
          "label": "Space between"
        },
        {
          "value": "tw:md:justify-evenly",
          "label": "Space evenly"
        }
      ],
      "default": "tw:md:justify-start",
      "label": "Vertical",
      "info": "(CSS class: justify-content)"
    },
    {
      "type": "select",
      "id": "desktop_align",
      "options": [
        {
          "value": "tw:md:items-stretch",
          "label": "Stretch"
        },
        {
          "value": "tw:md:items-start",
          "label": "Left"
        },
        {
          "value": "tw:md:items-center",
          "label": "Center"
        },
        {
          "value": "tw:md:items-end",
          "label": "Right"
        }
      ],
      "default": "tw:md:items-start",
      "label": "Horizontal",
      "info": "(CSS class: align-items)"
    },
    {
      "type": "select",
      "id": "desktop_text_align",
      "options": [
        {
          "value": "tw:md:text-left",
          "label": "Left"
        },
        {
          "value": "tw:md:text-center",
          "label": "Center"
        },
        {
          "value": "tw:md:text-right",
          "label": "Right"
        },
      ],
      "default": "tw:md:text-left",
      "label": "Text alignmenet",
      "info": "When using blocks with text that wraps, you may need to align the text as well (CSS class: text-align)"
    },
    {
      "type": "select",
      "id": "desktop_gap",
      "options": [
        {
          "value": "tw:md:gap-0",
          "label": "0"
        },
        {
          "value": "tw:md:gap-[4px]",
          "label": "4px"
        },
        {
          "value": "tw:md:gap-[8px]",
          "label": "8px"
        },
        {
          "value": "tw:md:gap-[16px]",
          "label": "16px"
        },
        {
          "value": "tw:md:gap-[24px]",
          "label": "24px"
        },
        {
          "value": "tw:md:gap-[32px]",
          "label": "32px"
        },
      ],
      "default": "tw:md:gap-[8px]",
      "label": "Gap"
    },
    {
      "type": "select",
      "id": "desktop_padding",
      "options": [
        {
          "value": "tw:md:p-0",
          "label": "0"
        },
        {
          "value": "tw:md:p-[4px]",
          "label": "4px"
        },
        {
          "value": "tw:md:p-[8px]",
          "label": "8px"
        },
        {
          "value": "tw:md:p-[16px]",
          "label": "16px"
        },
        {
          "value": "tw:md:p-[24px]",
          "label": "24px"
        },
        {
          "value": "tw:md:p-[32px]",
          "label": "32px"
        },
      ],
      "default": "tw:md:p-[16px]",
      "label": "Padding"
    },
    {
      "type": "header",
      "content": "Advanced settings",
      "info": "t:sections.all.advanced_options.label"
    },
    {
      "type": "text",
      "id": "custom_classes",
      "label": "Block custom classes"
    }
  ],
  "presets": [
    {
      "name": "Flexbox"
    }
  ]
}
{% endschema %}
