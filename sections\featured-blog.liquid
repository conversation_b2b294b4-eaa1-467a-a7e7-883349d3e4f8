{% comment %}
  bs-add
  - ability to hide/show section
  - ability to have separate padding/margin for desktop/mobile
  - new color options and also gradient flag for colors
  - ability to override heading with liquid
{% endcomment %}

{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-article-card.css' | asset_url | stylesheet_tag }}
{{ 'section-featured-blog.css' | asset_url | stylesheet_tag }}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

{%- liquid
  assign posts_displayed = section.settings.blog.articles_count
  if section.settings.post_limit <= section.settings.blog.articles_count or section.settings.post_limit <= 4
    assign posts_exceed_limit = true
    assign posts_displayed = section.settings.post_limit
  endif

  assign heading_content = section.settings.heading_liquid | default: section.settings.heading
-%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="blog color-{{ section.settings.color_scheme }} gradient{% if heading_content == blank %} no-heading{% endif %}">
    <div class="page-width-desktop isolate{% if posts_displayed < 3 %} page-width-tablet{% endif %} section-{{ section.id }}-padding">
      {%- unless heading_content == blank -%}
        <div class="title-wrapper-with-link{% if posts_displayed > 2 %} title-wrapper--self-padded-tablet-down{% else %} title-wrapper--self-padded-mobile{% endif %} title-wrapper--no-top-margin">
        <h2
          id="SectionHeading-{{ section.id }}"
          class="blog__title inline-richtext {{ section.settings.heading_size }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
          {% if settings.animations_reveal_on_scroll %}
            data-cascade
          {% endif %}
        >
            {{ heading_content }}
          </h2>
  
          {%- if section.settings.blog != blank
            and section.settings.show_view_all
            and section.settings.post_limit < section.settings.blog.articles_count
          -%}
            <a
              href="{{ section.settings.blog.url }}"
              class="link underlined-link large-up-hide{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
            >
              {{ 'sections.featured_blog.view_all' | t }}
            </a>
          {%- endif -%}
        </div>
      {%- endunless -%}

      <slider-component class="slider-mobile-gutter{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        <ul
          id="Slider-{{ section.id }}"
          class="blog__posts articles-wrapper contains-card contains-card--article{% if settings.blog_card_style == 'standard' %} contains-card--standard{% endif %} grid grid--peek grid--2-col-tablet grid--{{ section.settings.columns_desktop }}-col-desktop slider {% if posts_displayed > 2 %}slider--tablet{% else %}slider--mobile{% endif %}"
          role="list"
        >
          {%- if section.settings.blog != blank and section.settings.blog.articles_count > 0 -%}
            {%- for article in section.settings.blog.articles limit: section.settings.post_limit -%}
              <li
                id="Slide-{{ section.id }}-{{ forloop.index }}"
                class="blog__post grid__item article slider__slide slider__slide--full-width{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                {% if settings.animations_reveal_on_scroll %}
                  data-cascade
                  style="--animation-order: {{ forloop.index }};"
                {% endif %}
              >
                {% render 'article-card',
                  blog: section.settings.blog,
                  article: article,
                  media_aspect_ratio: 1.66,
                  show_image: section.settings.show_image,
                  show_tags: section.settings.show_tags,
                  show_date: section.settings.show_date,
                  show_author: section.settings.show_author,
                  show_excerpt: true,
                  card_heading_font_size: section.settings.card_heading_font_size
                %}
              </li>
            {%- endfor -%}
          {%- else -%}
            {% for i in (1..section.settings.post_limit) -%}
              {%- assign placeholder_image_index = forloop.index0 | modulo: 3 | plus: 1 -%}
              {%- assign placeholder_image = 'blog-apparel-' | append: placeholder_image_index -%}
              <li
                id="Slide-{{ section.id }}-{{ forloop.index }}"
                class="blog__post grid__item article slider__slide slider__slide--full-width{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
                {% if settings.animations_reveal_on_scroll %}
                  data-cascade
                  style="--animation-order: {{ forloop.index }};"
                {% endif %}
              >
                <div class="article-card-wrapper card-wrapper{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
                  <div
                    class="
                      card article-card card--{{ settings.blog_card_style }}
                      {% if settings.blog_card_style == 'card' %} color-{{ settings.blog_card_color_scheme }} gradient{% endif %}
                      {% if section.settings.show_image %} card--media{% else %} card--text{% endif %}
                    "
                    style=" {% if settings.blog_card_style == 'standard' %} --ratio-percent: 100%;{% elsif settings.blog_card_style == 'card' %} --ratio-percent: 0%;{% endif %}"
                  >
                    <div
                      class="card__inner{% if settings.blog_card_style == 'standard' %} color-{{ settings.blog_card_color_scheme }} gradient{% endif %} ratio"
                      style="--ratio-percent: 80%;"
                    >
                      {%- if section.settings.show_image == true -%}
                        <div class="article-card__image-wrapper card__media">
                          <div class="article-card__image media">
                            {{ placeholder_image | placeholder_svg_tag: 'blog-placeholder-svg' }}
                          </div>
                        </div>
                      {%- endif -%}
                      <div class="card__content">
                        <div class="card__information">
                          <h3 class="card__heading {{ section.settings.card_heading_font_size }}">
                            {{ 'sections.featured_blog.onboarding_title' | t }}
                          </h3>
                          <p class="article-card__excerpt rte-width">
                            {{ 'sections.featured_blog.onboarding_content' | t }}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div class="card__content">
                      <div class="card__information">
                        <h3 class="card__heading {{ section.settings.card_heading_font_size }}">
                          {{ 'sections.featured_blog.onboarding_title' | t }}
                        </h3>
                        <p class="article-card__excerpt rte-width">
                          {{ 'sections.featured_blog.onboarding_content' | t }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            {%- endfor -%}
          {%- endif -%}
        </ul>
        {%- if posts_exceed_limit -%}
          <div class="slider-buttons{% if section.settings.post_limit < 3 %} medium-hide{% endif %}{% if section.settings.post_limit < 2 %} small-hide{% endif %}">
            <button
              type="button"
              class="slider-button slider-button--prev"
              name="previous"
              aria-label="{{ 'general.slider.previous_slide' | t }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
            <div class="slider-counter caption">
              <span class="slider-counter--current">1</span>
              <span aria-hidden="true"> / </span>
              <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
              <span class="slider-counter--total">{{ section.settings.post_limit }}</span>
            </div>
            <button
              type="button"
              class="slider-button slider-button--next"
              name="next"
              aria-label="{{ 'general.slider.next_slide' | t }}"
            >
              <span class="svg-wrapper">
                {{- 'icon-caret.svg' | inline_asset_content -}}
              </span>
            </button>
          </div>
        {%- endif -%}
      </slider-component>

      {%- if section.settings.show_view_all and section.settings.post_limit < section.settings.blog.articles_count -%}
        <div
          class="blog__view-all center small-hide medium-hide{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
          {% if settings.animations_reveal_on_scroll %}
            data-cascade
          {% endif %}
        >
          <a
            href="{{ section.settings.blog.url }}"
            id="ViewAll-{{ section.id }}"
            class="blog__button {{ section.settings.button_style }}"
            aria-labelledby="ViewAll-{{ section.id }} SectionHeading-{{ section.id }}"
          >
            {{ 'sections.featured_blog.view_all' | t }}
          </a>
        </div>
      {%- endif -%}
    </div>
  </div>
</div>

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.featured-blog.name",
  "tag": "section",
  "class": "section section--featured-blog",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "t:sections.featured-blog.settings.heading.default",
      "label": "t:sections.featured-blog.settings.heading.label"
    },
    {
      "type": "liquid",
      "id": "heading_liquid",
      "label": "Heading liquid",
      "info": "Overrides Heading above"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "blog",
      "id": "blog",
      "label": "t:sections.featured-blog.settings.blog.label"
    },
    {
      "type": "range",
      "id": "post_limit",
      "min": 2,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.post_limit.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.columns_desktop.label"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info",
      "default": "scheme-1"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_image.label",
      "info": "t:sections.featured-blog.settings.show_image.info"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_date.label"
    },
    {
      "type": "checkbox",
      "id": "show_tags",
      "default": false,
      "label": "t:sections.main-blog.settings.show_tags.label",
      "info": "Adjust badge settings in [theme settings](/editor?context=theme&category=badges)"
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "default": false,
      "label": "t:sections.featured-blog.settings.show_author.label"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_view_all.label"
    },
    {
      "type": "select",
      "id": "button_style",
      "options": [
        {
          "value": "button button--primary",
          "label": "Solid"
        },
        {
          "value": "button button--secondary",
          "label": "Outline"
        },
        {
          "value": "link underlined-link",
          "label": "Link"
        },
        {
          "value": "button button-1",
          "label": "Custom 1"
        },
        {
          "value": "button button-2",
          "label": "Custom 2"
        },
        {
          "value": "button button-3",
          "label": "Custom 3"
        },
        {
          "value": "button button-4",
          "label": "Custom 4"
        },
        {
          "value": "button button-5",
          "label": "Custom 5"
        }
      ],
      "default": "button button--primary",
      "label": "Button style"
    },
    {
      "type": "select",
      "id": "card_heading_font_size",
      "options": [
        {
          "value": "h6",
          "label": "t:sections.all.heading_size.options__h6.label"
        },
        {
          "value": "h5",
          "label": "t:sections.all.heading_size.options__h5.label"
        },
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__h4.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__h3.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        }
      ],
      "default": "h2",
      "label": "Article card heading font size"
    },
    {
      "type": "header",
      "content": "Section spacing mobile (<750px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section spacing desktop (>750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 36
    },
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-blog.presets.name"
    }
  ]
}
{% endschema %}