{"sections": {"rich_text_NVGRyP": {"type": "rich-text", "blocks": {"custom_liquid_8hLyfk": {"type": "custom_liquid", "settings": {"title": "Heading", "custom_liquid": "<h1 class=\"h2 tw:mb-0\">DownShift</h3>\n<p class=\"tw:mt-4! body-copy-large tw:max-w-3xl tw:mx-auto\">A familiar mellow buzz that helps you unwind from the grind without feeling hazy or overwhelmed.\n</p>"}}, "custom_liquid_b6HPFw": {"type": "custom_liquid", "settings": {"title": "Flavor Options", "custom_liquid": "{%- if product.metafields.custom.linked_products != blank -%}\n  <div class=\"fake-variants\">\n    <ul class=\"tw:flex tw:flex-wrap tw:gap-4 tw:justify-center\">\n      {%- for fake_variant in product.metafields.custom.linked_products.value -%}\n        <li\n          data-product-handle=\"{{ fake_variant.handle }}\"\n        >\n          {%- if fake_variant.handle == product.handle -%}\n            <span class=\"h4 tw:md:text-[18px] tw:text-[15px] tw:uppercase tw:font-bold tw:flex tw:px-4 tw:py-2 tw:w-fit tw:rounded-xl tw:border-2 tw:border-[#2B2D20]\" style=\"color: {{ product.metafields.custom.flavor_color | default: '#D7DBC6' }}; background: #2B2D20;\">{{ fake_variant.metafields.custom.linked_products_name }}</span>\n          {%- else -%}\n              <a class=\"h4 tw:md:text-[18px] tw:text-[15px] tw:no-underline tw:uppercasel tw:font-bold tw:flex tw:px-4 tw:py-2 tw:w-fit tw:border-2 tw:rounded-xl tw:border-[#2B2D20]\" href=\"{{ fake_variant.variants[1].url }}\">{{ fake_variant.metafields.custom.linked_products_name }}</a>\n          {%- endif -%}\n        </li>\n      {%- endfor -%}\n    </ul>\n  </div>\n{%- endif -%}"}}}, "block_order": ["custom_liquid_8hLyfk", "custom_liquid_b6HPFw"], "custom_css": [], "settings": {"desktop_content_position": "full-width", "content_alignment": "center", "color_scheme": "", "padding_top_mobile": 20, "padding_top": 10, "padding_bottom_mobile": 20, "padding_bottom": 40, "page": "", "hide_size": "", "show_color_scheme_content": false, "color_scheme_content": "", "content_width": "page-width", "content_max_width_tablet": 900, "content_max_width_desktop": 1600, "mobile_content_alignment": "", "padding_left_mobile": 0, "padding_left_desktop": 0, "padding_right_mobile": 0, "padding_right_desktop": 0, "custom_css_class": "", "custom_liquid": ""}}, "main": {"type": "main-product", "blocks": {"custom_liquid_cadKbN": {"type": "custom_liquid", "disabled": true, "settings": {"title": "My custom liquid", "custom_liquid": "<h2 class=\"h4 tw:uppercase tw:font-bold tw:flex tw:px-3 tw:py-2 tw:w-fit tw:rounded-xl tw:border-2 tw:border-[#2B2D20]\" style=\"color: {{ product.metafields.custom.flavor_color | default: '#D7DBC6' }}; background: #2B2D20;\">{{ product.metafields.custom.linked_products_name }}</h2>", "hide_size": "", "custom_css": ""}}, "custom_liquid_4weG3j": {"type": "custom_liquid", "settings": {"title": "My custom liquid", "custom_liquid": "<p class=\"body-copy-large\">{{ product.metafields.custom.flavor_information }}</p.", "hide_size": "", "custom_css": ""}}, "custom_liquid_QkUVt9": {"type": "custom_liquid", "disabled": true, "settings": {"title": "Flavor options", "custom_liquid": "{%- if product.metafields.custom.linked_products != blank -%}\n  <div class=\"fake-variants\">\n    <ul class=\"tw:flex tw:flex-wrap tw:gap-3\">\n      {%- for fake_variant in product.metafields.custom.linked_products.value -%}\n        <li\n          data-product-handle=\"{{ fake_variant.handle }}\"\n        >\n          {%- if fake_variant.handle == product.handle -%}\n            <span class=\"h4 tw:text-[18px] tw:uppercase tw:font-bold tw:flex tw:px-3 tw:py-2 tw:w-fit tw:rounded-xl tw:border-2 tw:border-[#2B2D20]\" style=\"color: {{ product.metafields.custom.flavor_color | default: '#D7DBC6' }}; background: #2B2D20;\">{{ fake_variant.metafields.custom.linked_products_name }}</span>\n          {%- else -%}\n              <a class=\"h4 tw:text-[18px] tw:no-underline tw:uppercasel tw:font-bold tw:flex tw:px-3 tw:py-2 tw:w-fit tw:border-2 tw:rounded-xl tw:border-[#2B2D20]\" href=\"{{ fake_variant.variants[1].url }}\">{{ fake_variant.metafields.custom.linked_products_name }}</a>\n          {%- endif -%}\n        </li>\n      {%- endfor -%}\n    </ul>\n  </div>\n{%- endif -%}", "hide_size": "", "custom_css": ""}}, "fake_variant_picker_EktwnD": {"type": "fake_variant_picker", "disabled": true, "settings": {"fake_variants_name": "", "show_color_swatches": false, "show_image_swatches": false, "enable_quick_fake_variant_change": false, "hide_size": "", "liquid_before": "", "liquid_after": "", "custom_css": ""}}, "variant_picker": {"type": "variant_picker", "settings": {"picker_type": "dropdown", "swatch_shape": "circle", "hide_single_product_variant_swatches": false, "hide_size": "", "liquid_before": "", "liquid_after": "", "custom_css": ""}}, "subscriptions_app_block_UaxtFL": {"type": "shopify://apps/subscriptions/blocks/app-block/a3bfe9ec-96f8-4508-a003-df608a36d2ad", "settings": {"color_text_title": "#2b2d20", "color_text_body": "#2b2d20", "dividers_color": "#2b2d20", "bacgkround_color": "#f7f7f3", "border_thickness": 2, "border_radius": 10, "supporting_text_title": "Purchase Options", "subscription_policy_url": "shopify://policies/subscription-policy", "product": "{{product}}"}}, "buy_buttons": {"type": "buy_buttons", "settings": {"show_dynamic_checkout": false, "custom_liquid_buttons": "", "button_style": "button button-2", "show_gift_card_recipient": false, "hide_size": "", "liquid_before": "", "liquid_after": "", "custom_css": ""}}, "custom_liquid_TRdWwr": {"type": "custom_liquid", "settings": {"title": "Icons gallery", "custom_liquid": "<style>\n  #shopify-section-{{ section.id }} .product__media-wrapper {\n    position: relative;\n  }\n  #shopify-section-{{ section.id }} .product__media-wrapper::after {\n    content: url('{{ images['pdp_icon_made_from_thc.svg'] | image_url: width: 70 }}');\n    position: absolute;\n    top: 8px;\n    right: 8px;\n    z-index: 1;\n  }\n/*\n  #shopify-section-{{ section.id }} .product__media-wrapper::before {\n    content: url('{{ images['10_mg_cbd.svg'] | image_url: width: 70 }}');\n    position: absolute;\n    bottom: 0;\n    left: 7rem;\n    z-index: 1;\n  }\n*/\n  @media screen and (max-width: 749px) {\n    #shopify-section-{{ section.id }} .product__media-wrapper::after {\n      content: none;\n      width: 100px;\n      height: 100px;\n    }\n    #shopify-section-{{ section.id }} .product__media-wrapper::before {\n      content: none;\n      width: 75px;\n      height: 75px;\n    }\n  }\n  </style>", "hide_size": "", "custom_css": ""}}, "b731f803-b1a8-40bd-92d8-d90f0b4ab00a": {"type": "popup", "disabled": true, "settings": {"text": "<strong>Supplement Facts</strong>", "text_liquid": "", "title": "{{ product.title }}", "page": "{{ product.metafields.custom.nutrition_facts_page.value }}", "content_liquid": "", "color_scheme": "scheme-2", "hide_size": "", "liquid_before": "", "liquid_after": "", "custom_css": ""}}}, "block_order": ["custom_liquid_cadKbN", "custom_liquid_4weG3j", "custom_liquid_QkUVt9", "fake_variant_picker_EktwnD", "variant_picker", "subscriptions_app_block_UaxtFL", "buy_buttons", "custom_liquid_TRdWwr", "b731f803-b1a8-40bd-92d8-d90f0b4ab00a"], "custom_css": [".product-form__input .form__label {text-transform: uppercase; font-weight: 700; font-size: 1.6rem;}", ".product-popup-modal__opener {margin-top: -1.5rem;}", ".product {align-items: center;}", ".fake-variants .form__label {text-transform: uppercase; font-weight: 700; font-size: 1.6rem;}"], "settings": {"enable_sticky_info": false, "color_scheme": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba", "media_size": "small", "constrain_to_viewport": false, "media_fit": "contain", "gallery_layout": "stacked", "media_position": "left", "image_zoom": "none", "mobile_thumbnails": "hide", "hide_variants": true, "enable_video_looping": false, "accordion_padding": 0.5, "accordion_title_size": "h6", "padding_top_mobile": 16, "padding_bottom_mobile": 16, "padding_top": 36, "padding_bottom": 36, "custom_liquid_column": "", "custom_column_placement": "first", "custom_css_class": "", "custom_liquid": "<style>\n.shopify_subscriptions_app_policy {\ndisplay: none;\n} \n\n.shopify_subscriptions_app_block_label:has(input:checked) {\ncolor: #D7DBC6;\nbackground: #2B2D20;\n}\n\n.shopify_subscriptions_app_container {\n    max-width: 47.5rem;\n  }\n  .shopify_subscriptions_app__title {\n    text-transform: uppercase;\n    font-weight: 700;\n    font-size: 1.6rem;\n  }\n  .shopify_subscriptions_app_block_label > .shopify_subscriptions_purchase_option_wrapper > label {\n    margin-bottom: 1rem;\n  }\n  .shopify_subscriptions_app_block_label ul {\n    padding-left: 0;\n  }\n  .shopify_subscriptions_app_container input[type=radio] {\n    appearance: none;\n    margin: 0 .5rem 0 0;\n    font: inherit;\n    color: currentColor;\n    width: 1.15em;\n    height: 1.15em;\n    border: 0.15em solid currentColor;\n    border-radius: 50%;\n    transform: translateY(-0.075em);\n    display: inline-grid;\n    place-content: center;\n  }\n  .shopify_subscriptions_app_container input[type=radio]::before {\n    content: \"\";\n    width: 0.65em;\n    height: 0.65em;\n    border-radius: 50%;\n    transform: scale(0);\n    transition: 120ms transform ease-in-out;\n    box-shadow: inset 1em 1em currentColor;\n    background-color: CanvasText;\n  }\n  .shopify_subscriptions_app_container input[type=radio]:checked::before {\n    transform: scale(1);\n  }\n  .shopify_subscriptions_app_block_label_children li:not(:last-of-type) {\n    margin-bottom: 0.7rem;\n  }\n  .shopify_subscriptions_app_block_label_children label:hover,\n  .shopify_subscriptions_purchase_option_wrapper > label:hover {\n    cursor: pointer;\n  }\n  \n\n.shopify_subscriptions_fieldset div {\noverflow: hidden;\n}\n  /* styles for new badges\n  .fake-variants li:not(:first-of-type) .fake-variants__label::after {\n    content: '';\n    width: 30px;\n    height: 30px;\n    background-image: url('https://cdn.shopify.com/s/files/1/0672/2732/0565/files/new_badge_light.svg?v=1714487319');\n    background-size: contain;\n    background-position: center;\n    background-repeat: no-repeat;\n    position: absolute;\n    top: -1.5rem;\n    right: -0.5rem;\n  }\n  .fake-variants li:not(:first-of-type) a.fake-variants__label::after {\n    background-image: url('https://cdn.shopify.com/s/files/1/0672/2732/0565/files/new_badge_dark.svg?v=1714487319');\n  }\n*/\n\n  /* fake swatch mobile adjustments */\n  @media screen and (max-width: 749px) {\n    .fake-variants .fake-variants__label {\n      padding: 1rem 1.2rem;\n    }\n  }\nbody .fake-variants .fake-variants__label {\noverflow: visible;\n}\n.product__media-wrapper .media {\n  background: #F7F7F3;\nborder-radius: 10px;\n}\n</style>\n{% comment %}\n<script>\n  window.addEventListener('DOMContentLoaded', () => {\n    let downShiftProd = document.querySelector('variant-radios[data-url=\"/products/downshift\"]');\n    \n    if (downShiftProd) {\n      let locationHref = location.href;\n      let radioVariant = downShiftProd.querySelectorAll('input[name=\"Quantity\"]');\n      \n      if (radioVariant && radioVariant.length > 1 && locationHref.indexOf('?variant=') < 0) {\n        radioVariant[1].click();\n      }\n    }\n  });\n\ndocument.addEventListener('DOMContentLoaded', function() {\n  // Select all the label elements with the radio buttons using a unique data attribute\n  var labels = document.querySelectorAll('label input[data-radio-type=\"selling_plan\"]');\n\n  // Loop through each label\n  labels.forEach(function(labelInput) {\n    // Get the parent label element\n    var labelElement = labelInput.parentElement;\n\n    // Get the label's child text nodes only, excluding the input element\n    var labelText = Array.from(labelElement.childNodes).find(node => node.nodeType === Node.TEXT_NODE);\n\n    if (labelText) {\n      // Use a regular expression to remove \", 10% off\"\n      labelText.textContent = labelText.textContent.replace(/,\\s*\\d+%\\s*off/, '');\n    }\n  });\n});\n</script>\n{% endcomment %}"}}, "longshore_multicolumn_78yif8": {"type": "longshore-multicolumn", "blocks": {"column_RmLnnk": {"type": "_column", "settings": {"custom_color_scheme": true, "color_scheme": "scheme-2", "column_span_amount": "grid__item--span-1", "column_height_mobile": "tw:max-sm:self-stretch", "column_span_amount_desktop": "grid__item--span-1-tablet", "column_height_desktop": "tw:md:self-stretch", "custom_classes": "tw:overflow-hidden"}, "blocks": {"longshore_image_cxLLVF": {"type": "longshore-image", "settings": {"mobile_image": "shopify://shop_images/shift-naturals-asset-5551212.jpg", "desktop_image": "shopify://shop_images/shift-naturals-asset-5551212.jpg", "mobile_image_wrapper_css": "", "desktop_image_wrapper_css": "", "custom_classes": ""}}}, "block_order": ["longshore_image_cxLLVF"]}, "column_CtLrYP": {"type": "_column", "settings": {"custom_color_scheme": true, "color_scheme": "scheme-2", "column_span_amount": "grid__item--span-1", "column_height_mobile": "tw:max-sm:self-stretch", "column_span_amount_desktop": "grid__item--span-1-tablet", "column_height_desktop": "tw:md:self-stretch", "custom_classes": "tw:overflow-hidden"}, "blocks": {"longshore_image_YFBfYF": {"type": "longshore-image", "settings": {"mobile_image": "shopify://shop_images/shift-naturals-asset-5591219.jpg", "desktop_image": "shopify://shop_images/shift-naturals-asset-5591219.jpg", "mobile_image_wrapper_css": "", "desktop_image_wrapper_css": "", "custom_classes": ""}}}, "block_order": ["longshore_image_YFBfYF"]}, "column_6hbtTV": {"type": "_column", "settings": {"custom_color_scheme": true, "color_scheme": "scheme-2", "column_span_amount": "grid__item--span-1", "column_height_mobile": "tw:max-sm:self-stretch", "column_span_amount_desktop": "grid__item--span-1-tablet", "column_height_desktop": "tw:md:self-stretch", "custom_classes": "tw:overflow-hidden"}, "blocks": {"longshore_image_RtreYj": {"type": "longshore-image", "settings": {"mobile_image": "shopify://shop_images/shift-naturals-asset-5591219.jpg", "desktop_image": "shopify://shop_images/shift-naturals-asset-5591219.jpg", "mobile_image_wrapper_css": "", "desktop_image_wrapper_css": "", "custom_classes": ""}}}, "block_order": ["longshore_image_RtreYj"]}, "column_qPWXmG": {"type": "_column", "settings": {"custom_color_scheme": false, "color_scheme": "", "column_span_amount": "grid__item--span-1", "column_height_mobile": "tw:max-sm:self-stretch", "column_span_amount_desktop": "grid__item--span-3-tablet", "column_height_desktop": "tw:md:self-stretch", "custom_classes": ""}, "blocks": {"longshore_flexbox_UH7YUi": {"type": "longshore-flexbox", "settings": {"mobile_justify": "tw:justify-start", "mobile_align": "tw:items-start", "mobile_text_align": "tw:text-left", "mobile_gap": "tw:gap-[4px]", "mobile_padding": "tw:p-[8px]", "desktop_justify": "tw:md:justify-start", "desktop_align": "tw:md:items-center", "desktop_text_align": "tw:md:text-center", "desktop_gap": "tw:md:gap-0", "desktop_padding": "tw:md:p-[16px]", "custom_classes": ""}, "blocks": {"longshore_text_hHE4HP": {"type": "longshore-text", "settings": {"text": "<p>DownShift is a terpene-infused sparkling water - so it alters your mood, but in an all-natural and healthy way. It’s your go-to beverage for stress-free moments and mindful refreshment.</p>", "mobile_max_width": "tw:max-w-full", "desktop_max_width": "tw:md:max-w-4xl", "custom_css": ""}}, "longshore_buttons_NwUEi8": {"type": "longshore-buttons", "settings": {"button_1_text": "Learn more", "button_1_url": "shopify://pages/downshift-info", "button_1_style": "button button--primary", "button_1_css": "", "button_1_attributes": "", "button_2_text": "", "button_2_url": "", "button_2_style": "button button--primary", "button_2_css": "", "button_2_attributes": "", "custom_css": ""}}}, "block_order": ["longshore_text_hHE4HP", "longshore_buttons_NwUEi8"]}}, "block_order": ["longshore_flexbox_UH7YUi"]}}, "block_order": ["column_RmLnnk", "column_CtLrYP", "column_6hbtTV", "column_qPWXmG"], "settings": {"hide_size": "", "color_scheme_section": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba", "color_scheme_slider": "scheme-5e86c5e9-864b-45e5-816d-5323a2e498ba", "mobile_page_width": true, "columns_mobile": "1", "swipe_on_mobile": false, "mobile_slider_peek": false, "desktop_page_width": true, "section_width": "longshore-multicolumn-full", "columns_desktop": 3, "swipe_on_desktop": false, "desktop_slider_peek": false, "padding_top_mobile": 48, "padding_bottom_mobile": 48, "padding_top_desktop": 40, "padding_bottom_desktop": 40, "parent_div_custom_css": "", "slider_component_custom_css": "", "ul_custom_css": "", "slider_controls_css": "", "custom_liquid_before": "", "custom_liquid_after": ""}}, "related-products": {"type": "related-products", "disabled": true, "settings": {"heading": "You may also like", "heading_liquid": "", "heading_size": "h2", "products_to_show": 4, "columns_desktop": 4, "color_scheme": "scheme-2", "image_ratio": "", "image_shape": "", "show_secondary_image": "", "show_vendor": "", "show_rating": "", "quick_add": "none", "quick_add_behavior": "", "card_heading_font_size": "", "quick_add_button_style": "button button--primary", "show_alternative_title": "", "show_card_product_custom_field": "", "columns_mobile": "2", "recommendation_type": "related", "recommendation_product_list": [], "recommendation_collection": "", "padding_top_mobile": 36, "padding_bottom_mobile": 36, "padding_top_desktop": 36, "padding_bottom_desktop": 36, "hide_size": ""}}, "1728598940edd596e6": {"type": "apps", "blocks": {"junip_product_reviews_ugc_junip_product_review_NeQ3ER": {"type": "shopify://apps/junip/blocks/junip-product-review/dc14f5a8-ed15-41b1-ad08-cfba23f9789b", "settings": {"product": "{{product}}"}}}, "block_order": ["junip_product_reviews_ugc_junip_product_review_NeQ3ER"], "settings": {"include_margins": true, "hide_size": "", "color_scheme": "scheme-1", "padding_top_mobile": 0, "padding_bottom_mobile": 25, "padding_left_mobile": 0, "padding_right_mobile": 0, "padding_top_desktop": 0, "padding_bottom_desktop": 25, "padding_left_desktop": 0, "padding_right_desktop": 0, "custom_css_class": "", "custom_liquid": ""}}}, "order": ["rich_text_NVGRyP", "main", "longshore_multicolumn_78yif8", "related-products", "1728598940edd596e6"]}