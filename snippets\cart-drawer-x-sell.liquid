{%- if cart != empty and settings.cart_x_sell_collection != blank -%}
  {%- liquid
    # slider defaults
    assign products_per_page = 1
    assign slider_style = 'vertical'
  
    # product card defaults
    assign image_ratio = settings.cart_x_sell_image_ratio
    assign quick_add = 'standard'
    assign quick_add_behavior = 'add_first_variant'
    assign quick_add_button_style = settings.cart_x_sell_quick_add_button_style
    assign card_heading_font_size = settings.cart_x_sell_card_heading_font_size
  
    # set defaults
    assign cart_x_sell_product_count = 0
    assign cart_x_sell_collection = collections[settings.cart_x_sell_collection]

    # continue if valid collection with products
    if cart_x_sell_collection != blank and cart_x_sell_collection.products
      assign cart_x_sell_collection_products = cart_x_sell_collection.products | where: 'available'
      assign cart_x_sell_variant_id_list = ''
      assign cart_variant_id_list = cart.items | map: 'variant_id' | join: ','
      
      # create list of products not in cart to x-sell
      for cart_product in cart_x_sell_collection_products
        unless cart_variant_id_list contains cart_product.selected_or_first_available_variant.id
          assign cart_x_sell_variant_id_list = cart_x_sell_variant_id_list | append: cart_product.handle | append: ','
        endunless
      endfor
      
      assign cart_x_sell_product_list = cart_x_sell_variant_id_list | split: ','
      assign cart_x_sell_product_count = cart_x_sell_product_list | size
    endif
  -%}
  {%- if cart_x_sell_product_count > 0 -%}
    <aside aria-label="{{ 'accessibility.complementary_products' | t }}" class="drawer__x-sell color-{{ settings.cart_x_sell_color_scheme }} gradient">
      <div class="complementary-products__container complementary-products__container--padding content-container">
        <div class="summary__title rte">
          <h2 class="{{ settings.cart_x_sell_title_size }} accordion__title">
            {{ settings.cart_x_sell_title }}
          </h2>
        </div>
        <slideshow-component class="slider-mobile-gutter">
          {%- assign number_of_slides = cart_x_sell_product_count
            | plus: 0.0
            | divided_by: products_per_page
            | ceil
          -%}
          {%- liquid 
            assign is_slider_style_horizontal = false 
            if slider_style == 'horizontal'
              assign is_slider_style_horizontal = true
            endif
          -%}
          <div
            id="Slider-{{ section.id }}"
            data-id="{{ section.id }}"
            class="contains-card contains-card--product complementary-slider complementary-slider--{{ slider_style }} grid grid--1-col slider grid--peek slider--everywhere"
            role="list"
            {% if number_of_slides > 1 %}
              aria-label="{{ 'general.slider.name' | t }}"
            {% endif %}
          >
            {%- for i in (1..number_of_slides) -%}
              <div
                id="Slide-{{ block.id }}-{{ forloop.index }}"
                class="complementary-slide complementary-slide--{{ settings.card_style }} grid__item slider__slide slideshow__slide"
                tabindex="-1"
                role="group"
                {% if number_of_slides > 1 %}
                  aria-roledescription="{{ 'sections.slideshow.slide' | t }}"
                  aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                {% endif %}
              >
                <ul class="list-unstyled {% if is_slider_style_horizontal %}grid grid--{{ products_per_page }}-col grid--{{ products_per_page }}-col-desktop{% endif %}" role="list">
                  {%- for product_handle in cart_x_sell_product_list
                    limit: products_per_page
                    offset: continue
                  -%}
                    {%- assign cart_x_sell_product = all_products[product_handle] -%}
                    <li class="{% if is_slider_style_horizontal %}grid__item{% endif %}">
                      {%- if slider_style == 'horizontal' -%}
                        {% render 'card-product',
                          card_product: cart_x_sell_product,
                          media_aspect_ratio: image_ratio,
                          show_secondary_image: false,
                          lazy_load: false,
                          quick_add: quick_add,
                          quick_add_behavior: quick_add_behavior,
                          quick_add_button_style: quick_add_button_style,
                          card_heading_font_size: card_heading_font_size,
                          section_id: section.id,
                          card_style_override: settings.cart_x_sell_card_style,
                          card_color_scheme_override: settings.cart_x_sell_card_color_scheme
                        %}
                      {%- else -%}
                        {% render 'card-product',
                          card_product: cart_x_sell_product,
                          media_aspect_ratio: image_ratio,
                          show_secondary_image: false,
                          lazy_load: false,
                          quick_add: quick_add,
                          quick_add_behavior: quick_add_behavior,
                          quick_add_button_style: quick_add_button_style,
                          card_heading_font_size: card_heading_font_size,
                          section_id: section.id,
                          horizontal_class: true,
                          horizontal_quick_add: true,
                          card_style_override: settings.cart_x_sell_card_style,
                          card_color_scheme_override: settings.cart_x_sell_card_color_scheme
                      %}
                      {%- endif -%}
                    </li>
                  {%- endfor -%}
                </ul>
              </div>
            {%- endfor -%}
          </div>
        </slideshow-component>
      </div>
    </aside>

    {{ 'component-slider.css' | asset_url | stylesheet_tag }}
    {{ 'component-card.css' | asset_url | stylesheet_tag }}
    {{ 'component-complementary-products.css' | asset_url | stylesheet_tag }}
  {%- endif -%}
{%- endif -%}