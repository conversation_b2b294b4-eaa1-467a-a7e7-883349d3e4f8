{% comment %}
  bs-add
  - new setup for blocks being able to span
  - new setup for blocks to be on top or bottom part of footer
  - new setup for blocks with alignment
{% endcomment %}

{% comment %}theme-check-disable UndefinedObject{% endcomment %}
{{ 'section-footer.css' | asset_url | stylesheet_tag }}
{{ 'component-newsletter.css' | asset_url | stylesheet_tag }}
{{ 'component-list-menu.css' | asset_url | stylesheet_tag }}
{{ 'component-list-payment.css' | asset_url | stylesheet_tag }}
{{ 'component-list-social.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .footer {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
  }

  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
  }

  .section-{{ section.id }}-padding .footer__content-top {
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  .section-{{ section.id }}-padding .footer__content-bottom {
    padding-top: {{ section.settings.padding_top_2 | times: 0.75 | round: 0 }}px;
  }

  .section-{{ section.id }}-padding {
    padding-bottom: {{ section.settings.padding_bottom_2 | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .footer {
      margin-top: {{ section.settings.margin_top }}px;
    }

    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
    }

    .section-{{ section.id }}-padding .footer__content-top {
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }

    .section-{{ section.id }}-padding .footer__content-bottom {
      padding-top: {{ section.settings.padding_top_2 }}px;
    }

    .section-{{ section.id }}-padding {
      padding-bottom: {{ section.settings.padding_bottom_2 }}px;
    }
  }
{%- endstyle -%}

<footer class="footer color-{{ section.settings.color_scheme }} gradient section-{{ section.id }}-padding">
  {%- liquid
    capture footer_blocks_top
      for block in section.blocks
        if block.settings.footer_location == 'top'
          render 'footer-blocks', block: block
        endif
      endfor
    endcapture

    capture footer_blocks_bottom
      for block in section.blocks
        if block.settings.footer_location == 'bottom'
          render 'footer-blocks', block: block
        endif
      endfor
    endcapture
  -%}

  {%- if footer_blocks_top != blank -%}
    <div class="footer__content-top">
      <div class="page-width">
        <div class="footer__blocks-wrapper grid grid--{{ section.settings.columns_mobile }}-col grid--{{ section.settings.columns_tablet }}-col-tablet grid--{{ section.settings.columns_desktop }}-col-desktop{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
          {% if settings.animations_reveal_on_scroll %}
            data-cascade
          {% endif %}
        >
          {{ footer_blocks_top }}
        </div>
      </div>
    </div>
  {%- endif -%}

  {%- if footer_blocks_bottom != blank -%}
    <div
      class="footer__content-bottom{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
      {% if settings.animations_reveal_on_scroll %}
        data-cascade
      {% endif %}
    >
      <div class="page-width">
        <div class="footer__content-bottom-wrapper grid grid--{{ section.settings.columns_footer_mobile }}-col grid--{{ section.settings.columns_footer_tablet }}-col-tablet grid--{{ section.settings.columns_footer_desktop }}-col-desktop">
          {{ footer_blocks_bottom }}
        </div>
      </div>
    </div>
  {%- endif -%}
</footer>

{% schema %}
{
  "name": "t:sections.footer.name",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.custom-liquid.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "default": "My custom liquid",
          "label": "Title",
          "info": "Only used to organize custom liquid in side bar."
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.custom-liquid.settings.custom_liquid.label",
          "info": "t:sections.custom-liquid.settings.custom_liquid.info"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "link_list",
      "name": "t:sections.footer.blocks.link_list.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.footer.blocks.link_list.settings.heading.default",
          "label": "t:sections.footer.blocks.link_list.settings.heading.label"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "link_list",
          "id": "menu",
          "default": "footer",
          "label": "t:sections.footer.blocks.link_list.settings.menu.label",
          "info": "t:sections.footer.blocks.link_list.settings.menu.info"
        },
        {
          "type": "checkbox",
          "id": "make_collapsible_row",
          "default": true,
          "label": "Show as collapsible row on mobile"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "brand_information",
      "name": "t:sections.footer.blocks.brand_information.name",
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.footer.blocks.brand_information.settings.paragraph.content"
        },
        {
          "type": "header",
          "content": "t:sections.footer.blocks.brand_information.settings.header__1.content"
        },
        {
          "type": "checkbox",
          "id": "show_social",
          "default": true,
          "label": "t:sections.footer.blocks.brand_information.settings.show_social.label",
          "info": "t:sections.footer.blocks.brand_information.settings.show_social.info"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.footer.blocks.text.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.footer.blocks.text.settings.heading.default",
          "label": "t:sections.footer.blocks.text.settings.heading.label"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "richtext",
          "id": "subtext",
          "default": "t:sections.footer.blocks.text.settings.subtext.default",
          "label": "t:sections.footer.blocks.text.settings.subtext.label"
        },
        {
          "type": "liquid",
          "id": "subtext_liquid",
          "label": "Subtext liquid",
          "info": "Overrides Subtext above"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "range",
          "id": "image_width",
          "min": 50,
          "max": 550,
          "step": 5,
          "unit": "px",
          "label": "Image width",
          "default": 100
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Image alignment on large screen",
          "options": [
            {
              "value": "",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "newsletter",
      "name": "Newsletter",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.footer.settings.newsletter_heading.default",
          "label": "Heading"
        },
        {
          "type": "liquid",
          "id": "heading_liquid",
          "label": "Heading liquid",
          "info": "Overrides Heading above"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "social",
      "name": "Social",
      "settings": [
        {
          "type": "header",
          "content": "t:sections.footer.settings.header__2.content",
          "info": "t:sections.footer.settings.header__2.info"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "bottom",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "country_language",
      "name": "Country and Language",
      "settings": [
        {
          "type": "header",
          "content": "t:sections.footer.settings.header__3.content",
          "info": "t:sections.footer.settings.header__4.info"
        },
        {
          "type": "checkbox",
          "id": "enable_country_selector",
          "default": true,
          "label": "t:sections.footer.settings.enable_country_selector.label"
        },
        {
          "type": "header",
          "content": "t:sections.footer.settings.header__5.content",
          "info": "t:sections.footer.settings.header__6.info"
        },
        {
          "type": "checkbox",
          "id": "enable_language_selector",
          "default": true,
          "label": "t:sections.footer.settings.enable_language_selector.label"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "bottom",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "copyright_payments",
      "name": "Copyright and Payments",
      "settings": [
        {
          "type": "header",
          "content": "t:sections.footer.settings.header__7.content"
        },
        {
          "type": "checkbox",
          "id": "payment_enable",
          "default": false,
          "label": "t:sections.footer.settings.payment_enable.label"
        },
        {
          "type": "header",
          "content": "t:sections.footer.settings.header__8.content",
          "info": "t:sections.footer.settings.header__8.info"
        },
        {
          "type": "checkbox",
          "id": "show_policy",
          "default": true,
          "label": "t:sections.footer.settings.show_policy.label"
        },
        {
          "type": "link_list",
          "id": "additional_policy_menu",
          "label": "Additional Policy Menu"
        },
        {
          "type": "select",
          "id": "additional_policy_menu_order",
          "options": [
            {
              "value": "before",
              "label": "Before"
            },
            {
              "value": "after",
              "label": "After"
            }
          ],
          "default": "after",
          "label": "Additional Policy Menu location"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "bottom",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    },
    {
      "type": "follow_on_shop",
      "name": "Follow on Shop Pay",
      "settings": [
        {
          "type": "header",
          "content": "t:sections.footer.settings.header__9.content",
          "info": "t:sections.footer.settings.header__9.info"
        },
        {
          "type": "header",
          "content": "Footer location"
        },
        {
          "type": "select",
          "id": "footer_location",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ],
          "default": "top",
          "label": "Footer location"
        },
        {
          "type": "header",
          "content": "Content options"
        },
        {
          "type": "select",
          "id": "content_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (mobile)"
        },
        {
          "type": "select",
          "id": "content_alignment_desktop",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "left",
          "label": "Content alignment (desktop)"
        },
        {
          "type": "header",
          "content": "Column span options"
        },
        {
          "type": "select",
          "id": "colomn_span_amount",
          "options": [
            {
              "value": "grid__item--span-1",
              "label": "1"
            },
            {
              "value": "grid__item--span-2",
              "label": "2"
            }
          ],
          "default": "grid__item--span-1",
          "label": "Column span amount (mobile)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_tablet",
          "options": [
            {
              "value": "grid__item--span-1-tablet",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-tablet",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-tablet",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-tablet",
              "label": "4"
            }
          ],
          "default": "grid__item--span-1-tablet",
          "label": "Column span (tablet only)",
          "info": "Screen (min-width: 750px) and (max-width: 989px)"
        },
        {
          "type": "select",
          "id": "colomn_span_amount_desktop",
          "options": [
            {
              "value": "grid__item--span-1-desktop",
              "label": "1"
            },
            {
              "value": "grid__item--span-2-desktop",
              "label": "2"
            },
            {
              "value": "grid__item--span-3-desktop",
              "label": "3"
            },
            {
              "value": "grid__item--span-4-desktop",
              "label": "4"
            },
            {
              "value": "grid__item--span-5-desktop",
              "label": "5"
            },
            {
              "value": "grid__item--span-6-desktop",
              "label": "6"
            }
          ],
          "default": "grid__item--span-1-desktop",
          "label": "Column span (desktop only)",
          "info": "Screen (min-width: 990px)"
        },
        {
          "type": "header",
          "content": "Hide options"
        },
        {
          "type": "select",
          "id": "hide_size",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "small-hide",
              "label": "Small (<750px)"
            },
            {
              "value": "small-hide medium-hide",
              "label": "Small and Medium (<990px)"
            },
            {
              "value": "medium-hide",
              "label": "Medium (>749px and <990px)"
            },
            {
              "value": "medium-hide large-up-hide",
              "label": "Medium and Large (>749px)"
            },
            {
              "value": "large-up-hide",
              "label": "Large (>989px)"
            }
          ],
          "default": "",
          "label": "Hide on screen size"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "Columns top"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.multicolumn.settings.columns_mobile.label"
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 2,
      "label": "Number of columns on tablet"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "t:sections.multicolumn.settings.columns_desktop.label"
    },
    {
      "type": "header",
      "content": "Columns bottom"
    },
    {
      "type": "select",
      "id": "columns_footer_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.multicolumn.settings.columns_mobile.label"
    },
    {
      "type": "range",
      "id": "columns_footer_tablet",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 2,
      "label": "Number of columns on tablet"
    },
    {
      "type": "range",
      "id": "columns_footer_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "t:sections.multicolumn.settings.columns_desktop.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.footer.settings.margin_top.label",
      "default": 0
    },
    {
      "type": "header",
      "content": "Section padding (top part)"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "Section padding (bottom part)"
    },
    {
      "type": "range",
      "id": "padding_top_2",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom_2",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "link_list"
      },
      {
        "type": "text"
      },
      {
        "type": "copyright_payments"
      }
    ]
  }
}
{% endschema %}
