<div id="block-{{ block.id }}" class="{{ block.settings.mobile_max_width }} {{ block.settings.desktop_max_width }} {{ block.settings.custom_css }}" {{ block.shopify_attributes }}>
  <details class="{{ block.settings.details_css }}">
    <summary class="link {{ block.settings.summary_css }}">
      <span id="open-text">{{ block.settings.summary_open }}</span>
      <span id="close-text">{{ block.settings.summary_close }}</span>
    </summary>
    {{ block.settings.content }}
  </details>
  {{ block.settings.text }}
</div>

<style>
#block-{{ block.id }} details #open-text, #block-{{ block.id }}  details[open] #close-text  {
  display: block;
}
#block-{{ block.id }} details[open] #open-text, #block-{{ block.id }} details #close-text  {
  display: none;
}
</style>

{% schema %}
{
  "name": "Collapsible text",
  "tag": null,
  "settings": [
    {
      "type": "text",
      "id": "summary_open",
      "label": "Summary open text"
    },
    {
      "type": "text",
      "id": "summary_close",
      "label": "Summary close text"
    },
    {
      "type": "richtext",
      "id": "content",
      "label": "Content"
    },
    {
      "type": "header",
      "content": "Mobile layout",
      "info": "Devices with screens < 750px"
    },
    {
      "type": "select",
      "id": "mobile_max_width",
      "options": [
        {
          "value": "tw:max-w-full",
          "label": "Full"
        },
        {
          "value": "tw:max-w-7xl",
          "label": "7xl"
        },
        {
          "value": "tw:max-w-6xl",
          "label": "6xl"
        },
        {
          "value": "tw:max-w-5xl",
          "label": "5xl"
        },
        {
          "value": "tw:max-w-4xl",
          "label": "4xl"
        },
        {
          "value": "tw:max-w-3xl",
          "label": "3xl"
        },
        {
          "value": "tw:max-w-2xl",
          "label": "2xl"
        },
        {
          "value": "tw:max-w-xl",
          "label": "xl"
        },
      ],
      "default": "tw:max-w-full",
      "label": "Max width"
    },
    {
      "type": "header",
      "content": "Desktop layout",
      "info": "Devices with screens >= 750px"
    },
    {
      "type": "select",
      "id": "desktop_max_width",
      "options": [
        {
          "value": "tw:md:max-w-full",
          "label": "Full"
        },
        {
          "value": "tw:md:max-w-7xl",
          "label": "7xl"
        },
        {
          "value": "tw:md:max-w-6xl",
          "label": "6xl"
        },
        {
          "value": "tw:md:max-w-5xl",
          "label": "5xl"
        },
        {
          "value": "tw:md:max-w-4xl",
          "label": "4xl"
        },
        {
          "value": "tw:md:max-w-3xl",
          "label": "3xl"
        },
        {
          "value": "tw:md:max-w-2xl",
          "label": "2xl"
        },
        {
          "value": "tw:md:max-w-xl",
          "label": "xl"
        },
      ],
      "default": "tw:md:max-w-full",
      "label": "Max width"
    },
    {
      "type": "header",
      "content": "Advanced settings (optional)"
    },
    {
      "type": "text",
      "id": "details_css",
      "label": "<details> additional CSS classes"
    },
    {
      "type": "text",
      "id": "summary_css",
      "label": "<summary> additional CSS classes"
    },
    {
      "type": "text",
      "id": "custom_css",
      "label": "Wrapper div additional CSS classes"
    }
  ],
  "presets": [{ "name": "Collapsible text" }]
}
{% endschema %}