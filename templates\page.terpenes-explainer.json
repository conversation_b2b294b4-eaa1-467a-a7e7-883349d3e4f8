/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "1be5424f-c0ff-4f8b-a3f3-21054757489c": {
      "type": "image-banner",
      "blocks": {
        "heading": {
          "type": "heading",
          "settings": {
            "heading": "DISCOVER OUR TERPENES",
            "heading_liquid": "",
            "heading_size": "h0"
          }
        },
        "a5a2a79d-3843-4fa2-b72f-60dee67c3197": {
          "type": "custom_liquid",
          "settings": {
            "title": "Terp icon and highlights",
            "custom_liquid": "<div style=\"display: flex; gap: 2rem;margin-top: auto;padding-top: 2rem;width: 100%;max-width:450px;\">\n{{ images['TERP_BLEND_iconrow_light.svg'] \n  | image_url: width: 3200\n  | image_tag:\n    class: 'bon-image'\n}}\n</div>"
          }
        }
      },
      "block_order": [
        "heading",
        "a5a2a79d-3843-4fa2-b72f-60dee67c3197"
      ],
      "custom_css": [
        ".banner:after {background: linear-gradient( 180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100% ); opacity: 1;}"
      ],
      "settings": {
        "hide_size": "",
        "image": "shopify://shop_images/discover_our_terpenes_can_leaves_grass.jpg",
        "background_video": "",
        "image_height_desktop": "custom",
        "banner_minheight_desktop": 90,
        "banner_minheight_desktop_units": "vh",
        "desktop_content_position": "middle-left",
        "desktop_content_alignment": "left",
        "show_text_box": false,
        "buttons_bottom_desktop": false,
        "background_video_mobile": "",
        "image_behavior": "none",
        "image_height_mobile": "custom",
        "banner_minheight_mobile": 60,
        "banner_minheight_mobile_units": "rem",
        "mobile_content_alignment": "left",
        "show_text_below": false,
        "reverse_text_placement_mobile": false,
        "buttons_bottom_mobile": false,
        "overlay_gradient": "",
        "image_overlay_opacity_mobile": 0,
        "image_overlay_opacity": 0,
        "color_scheme": "scheme-2",
        "content_width": "full-width",
        "override_content_max_width": true,
        "content_max_width": 71,
        "content_max_width_desktop": 82,
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "color_scheme_section": "",
        "custom_css_class": "",
        "custom_liquid": "",
        "page": ""
      }
    },
    "main": {
      "type": "main-page",
      "disabled": true,
      "custom_css": [],
      "settings": {
        "heading_size": "h0",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 56,
        "padding_bottom_desktop": 56
      }
    },
    "8eeadbf9-c0f4-40a7-83d9-31f6cdd08cc8": {
      "type": "image-with-text",
      "blocks": {
        "5e9451aa-7bca-4e68-a6eb-2dc4780f4cc2": {
          "type": "heading",
          "settings": {
            "heading": "Unveil the Essence of Nature with Shift",
            "heading_liquid": "",
            "heading_size": "h3"
          }
        },
        "f399d2cc-35c2-40d6-abc3-a1e29be066f5": {
          "type": "text",
          "settings": {
            "text": "<p>Here at Shift Naturals Inc, we're really into plants. Not just because they look good, but because they're packed with some incredible compounds that can do wonders for Human health. We're talking about terpenes—those things that give pine trees and orange peels their amazing smells. But it's more than just a nice scent; these little guys have some serious science backing up their health benefits.</p>",
            "text_style": "subtitle"
          }
        }
      },
      "block_order": [
        "5e9451aa-7bca-4e68-a6eb-2dc4780f4cc2",
        "f399d2cc-35c2-40d6-abc3-a1e29be066f5"
      ],
      "custom_css": [],
      "settings": {
        "image": "shopify://shop_images/terpenes_1.jpg",
        "show_video_controls": false,
        "image_contain": false,
        "height": "small",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "adapt",
        "desktop_image_width": "medium",
        "layout": "image_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "",
        "image_behavior": "none",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 80,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 80,
        "padding_bottom_desktop": 80,
        "page": "",
        "hide_size": "",
        "color_scheme_section": "",
        "content_width": "page-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "section--image-with-text--custom",
        "custom_liquid": ""
      }
    },
    "d2898378-8989-46cb-8fbd-a011bfefef7b": {
      "type": "image-with-text",
      "blocks": {
        "f399d2cc-35c2-40d6-abc3-a1e29be066f5": {
          "type": "text",
          "settings": {
            "text": "<p>When these terpenes come together, they amplify each other's strengths—this is the entourage effect at work. It's like a symphony where every note is crucial, and the whole is more impactful than any solo performance. Our approach aims to mirror nature's complexity, delivering a holistic experience that supports your well-being in a way that single compounds can't match. It’s nature's synergy, captured in every product we make.</p>",
            "text_style": "subtitle"
          }
        }
      },
      "block_order": [
        "f399d2cc-35c2-40d6-abc3-a1e29be066f5"
      ],
      "custom_css": [],
      "settings": {
        "image": "shopify://shop_images/terpenes_2.jpg",
        "show_video_controls": false,
        "image_contain": false,
        "height": "small",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "adapt",
        "desktop_image_width": "medium",
        "layout": "text_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "",
        "image_behavior": "none",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 40,
        "padding_bottom_mobile": 40,
        "padding_top_desktop": 80,
        "padding_bottom_desktop": 160,
        "page": "",
        "hide_size": "",
        "color_scheme_section": "",
        "content_width": "page-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "section--image-with-text--custom",
        "custom_liquid": ""
      }
    },
    "f20ce790-27ba-442c-ba02-31935dddb131": {
      "type": "collapsible-content",
      "blocks": {
        "template--16999685226741__f20ce790-27ba-442c-ba02-31935dddb131-collapsible_row-1": {
          "type": "collapsible_row",
          "settings": {
            "heading": "What are Terpenes?",
            "icon": "none",
            "row_content": "<p>Terpenes exist in nearly all plants as they serve to protect the plant - some are anti-fungal, others anti-microbial, they help defend against predators, and even attract pollinators! In Humans terpenes have a wide array of therapeutic benefits. </p><p>Terpenes are aromatic compounds contributing to their flavor, scent, and colors. Love the soothing smell of lavender - those are terpenes, baby!  They are the essence of nature's bouquet, present in everything from the fresh pine of a forest to the sweet zest of citrus fruits.</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "template--16999685226741__f20ce790-27ba-442c-ba02-31935dddb131-collapsible_row-2": {
          "type": "collapsible_row",
          "settings": {
            "heading": "How Do Terpenes Work?",
            "icon": "none",
            "row_content": "<p>In nature, terpenes protect plants from environmental stresses and insect predators. In humans, they interact with our biological systems, influencing mood, stress levels, and overall well-being, through what's known as the '<a href=\"https://www.ncbi.nlm.nih.gov/pmc/articles/PMC7324885/\"><span style=\"text-decoration:underline\">entourage effect</span></a>' when combined with other natural compounds.</p><p>The interaction among the terpenes in a full-spectrum profile provides a balanced, nuanced experience. Myrcene, for instance, increases cell permeability, allowing for more efficient absorption of cannabinoids and terpenes, while pinene adds clarity, countering the sedative effects of more relaxing terpenes. Caryophyllene offers its anti-inflammatory prowess, and limonene and linalool bring their stress-relieving, mood-enhancing properties. Together, these terpenes work in concert to create an effect that's greater than the sum of its parts, targeting specific health concerns while promoting overall wellness.</p>",
            "row_content_liquid": "",
            "page": ""
          }
        },
        "template--16999685226741__f20ce790-27ba-442c-ba02-31935dddb131-collapsible_row-3": {
          "type": "collapsible_row",
          "settings": {
            "heading": "Why Choose Terpenes?",
            "icon": "none",
            "row_content": "<p>Nature has a perfect recipe for balance, and we're here to bottle it up. With a full-spectrum terpene profile, we capture the whole plant's essence, bringing together a diverse mix of nature's own compounds. This isn't about tinkering in the lab; it's about respecting what the earth has already mastered. Each terpene in the spectrum plays its part, from the calming effects of myrcene to the sharp clarity provided by pinene, resulting in a natural blend that's just right.</p><p>Each formulation aims to create a natural path to altering your state of mind, all while supplying your body with healthy plant based molecules.</p>",
            "row_content_liquid": "",
            "page": ""
          }
        }
      },
      "block_order": [
        "template--16999685226741__f20ce790-27ba-442c-ba02-31935dddb131-collapsible_row-1",
        "template--16999685226741__f20ce790-27ba-442c-ba02-31935dddb131-collapsible_row-2",
        "template--16999685226741__f20ce790-27ba-442c-ba02-31935dddb131-collapsible_row-3"
      ],
      "custom_css": [],
      "settings": {
        "caption": "",
        "heading": "",
        "heading_size": "h1",
        "heading_alignment": "center",
        "layout": "none",
        "container_color_scheme": "",
        "color_scheme": "background-2",
        "open_first_collapsible_row": true,
        "image": "shopify://shop_images/Prune.jpg",
        "image_ratio": "adapt",
        "desktop_layout": "image_first",
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0
      }
    },
    "f9ae08e7-85ec-48ba-b365-d3a6e2335aa9": {
      "type": "multicolumn",
      "blocks": {
        "template--16978522734837__02f712d2-0515-4d83-960e-8455e0e9e014-column-1": {
          "type": "column",
          "settings": {
            "image": "{{ page.metafields.custom.terpene_link_1.value.image.value }}",
            "title": "{{ page.metafields.custom.terpene_link_1.value.title.value }}",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<p>{{ page.metafields.custom.terpene_link_1.value.short_description.value }}</p><br>\n<p class=\"h5\" style=\"color: #7C8459;\">Scientific Studies</p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_1.value.article_1.value.link.value }}\">{{ page.metafields.custom.terpene_link_1.value.article_1.value.title.value }}</a></p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_1.value.article_2.value.link.value }}\">{{ page.metafields.custom.terpene_link_1.value.article_2.value.title.value }}</a></p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_1.value.article_3.value.link.value }}\">{{ page.metafields.custom.terpene_link_1.value.article_3.value.title.value }}</a></p><br>",
            "link_label": "Learn More",
            "link_label_liquid": "",
            "link": "{{ page.metafields.custom.terpene_link_1.value.page_url.value }}",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "42c8e126-e9a4-4f96-aefd-d7a7c0abfc93": {
          "type": "column",
          "settings": {
            "image": "{{ page.metafields.custom.terpene_link_2.value.image.value }}",
            "title": "{{ page.metafields.custom.terpene_link_2.value.title.value }}",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<p>{{ page.metafields.custom.terpene_link_2.value.short_description.value }}</p><br>\n<p class=\"h5\" style=\"color: #7C8459;\">Scientific Studies</p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_2.value.article_1.value.link.value }}\">{{ page.metafields.custom.terpene_link_2.value.article_1.value.title.value }}</a></p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_2.value.article_2.value.link.value }}\">{{ page.metafields.custom.terpene_link_2.value.article_2.value.title.value }}</a></p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_2.value.article_3.value.link.value }}\">{{ page.metafields.custom.terpene_link_2.value.article_3.value.title.value }}</a></p><br>",
            "link_label": "Learn More",
            "link_label_liquid": "",
            "link": "{{ page.metafields.custom.terpene_link_2.value.page_url.value }}",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "0ac2ae14-cd1a-4f78-947b-00d9333bbf37": {
          "type": "column",
          "settings": {
            "image": "{{ page.metafields.custom.terpene_link_3.value.image.value }}",
            "title": "{{ page.metafields.custom.terpene_link_3.value.title.value }}",
            "title_liquid": "",
            "text": "",
            "text_liquid": "<p>{{ page.metafields.custom.terpene_link_3.value.short_description.value }}</p><br>\n<p class=\"h5\" style=\"color: #7C8459;\">Scientific Studies</p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_3.value.article_1.value.link.value }}\">{{ page.metafields.custom.terpene_link_3.value.article_1.value.title.value }}</a></p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_3.value.article_2.value.link.value }}\">{{ page.metafields.custom.terpene_link_3.value.article_2.value.title.value }}</a></p><br>\n<p><a href=\"{{ page.metafields.custom.terpene_link_3.value.article_3.value.link.value }}\">{{ page.metafields.custom.terpene_link_3.value.article_3.value.title.value }}</a></p><br>",
            "link_label": "Learn More",
            "link_label_liquid": "",
            "link": "{{ page.metafields.custom.terpene_link_3.value.page_url.value }}",
            "button_style": "button button-2",
            "show_popup": false,
            "text_popup": "Link label",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "",
            "content_liquid_popup": "",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "template--16978522734837__02f712d2-0515-4d83-960e-8455e0e9e014-column-1",
        "42c8e126-e9a4-4f96-aefd-d7a7c0abfc93",
        "0ac2ae14-cd1a-4f78-947b-00d9333bbf37"
      ],
      "settings": {
        "hide_size": "medium-hide large-up-hide",
        "color_scheme_section": "",
        "color_scheme_content": "",
        "background_style": "primary",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 3,
        "columns_mobile": "1",
        "swipe_on_mobile": true,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "circle",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h4",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 40,
        "padding_bottom_mobile": 15,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": true,
        "page": "",
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "9be79833-3a8d-45fa-b207-03d30d44cdfa": {
      "type": "collapsible-content",
      "blocks": {
        "template--16978522734837__12733908-217e-4f35-907d-f93807e78db9-collapsible_row-1": {
          "type": "collapsible_row",
          "settings": {
            "heading": "{{ page.metafields.custom.terpene_link_1.value.title.value }}",
            "icon": "none",
            "row_content": "",
            "row_content_liquid": "<div style=\"padding-left: 3rem;\"><p style=\"font-weight: bold;\">{{ page.metafields.custom.terpene_link_1.value.short_description.value }}</p>\n<p class=\"h5\" style=\"color: #7C8459;\">Scientific Studies</p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_1.value.article_1.value.link.value }}\">{{ page.metafields.custom.terpene_link_1.value.article_1.value.title.value }}</a></p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_1.value.article_2.value.link.value }}\">{{ page.metafields.custom.terpene_link_1.value.article_2.value.title.value }}</a></p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_1.value.article_3.value.link.value }}\">{{ page.metafields.custom.terpene_link_1.value.article_3.value.title.value }}</a></p>\n<p><a class=\"button button-2\" href=\"{{ page.metafields.custom.terpene_link_1.value.page_url.value }}\">Learn more</a></p>\n</div>",
            "page": ""
          }
        },
        "6e7486ca-da1e-4584-87ca-0748c1c26483": {
          "type": "collapsible_row",
          "settings": {
            "heading": "{{ page.metafields.custom.terpene_link_2.value.title.value }}",
            "icon": "none",
            "row_content": "",
            "row_content_liquid": "<div style=\"padding-left: 3rem;\"><p style=\"font-weight: bold;\">{{ page.metafields.custom.terpene_link_2.value.short_description.value }}</p>\n<p class=\"h5\" style=\"color: #7C8459;\">Scientific Studies</p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_2.value.article_1.value.link.value }}\">{{ page.metafields.custom.terpene_link_2.value.article_1.value.title.value }}</a></p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_2.value.article_2.value.link.value }}\">{{ page.metafields.custom.terpene_link_2.value.article_2.value.title.value }}</a></p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_2.value.article_3.value.link.value }}\">{{ page.metafields.custom.terpene_link_2.value.article_3.value.title.value }}</a></p>\n<p><a class=\"button button-2\" href=\"{{ page.metafields.custom.terpene_link_2.value.page_url.value }}\">Learn more</a></p></div>",
            "page": ""
          }
        },
        "cb77d107-88ea-4063-a09e-85a623ab19ad": {
          "type": "collapsible_row",
          "settings": {
            "heading": "{{ page.metafields.custom.terpene_link_3.value.title.value }}",
            "icon": "none",
            "row_content": "",
            "row_content_liquid": "<div style=\"padding-left: 3rem;\"><p style=\"font-weight: bold;\">{{ page.metafields.custom.terpene_link_3.value.short_description.value }}</p>\n<p class=\"h5\" style=\"color: #7C8459;\">Scientific Studies</p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_3.value.article_1.value.link.value }}\">{{ page.metafields.custom.terpene_link_3.value.article_1.value.title.value }}</a></p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_3.value.article_2.value.link.value }}\">{{ page.metafields.custom.terpene_link_3.value.article_2.value.title.value }}</a></p>\n<p><a href=\"{{ page.metafields.custom.terpene_link_3.value.article_3.value.link.value }}\">{{ page.metafields.custom.terpene_link_3.value.article_3.value.title.value }}</a></p>\n<p><a class=\"button button-2\" href=\"{{ page.metafields.custom.terpene_link_3.value.page_url.value }}\">Learn more</a></p></div>",
            "page": ""
          }
        }
      },
      "block_order": [
        "template--16978522734837__12733908-217e-4f35-907d-f93807e78db9-collapsible_row-1",
        "6e7486ca-da1e-4584-87ca-0748c1c26483",
        "cb77d107-88ea-4063-a09e-85a623ab19ad"
      ],
      "custom_css": [],
      "settings": {
        "caption": "",
        "heading": "Learn More",
        "heading_size": "h1",
        "heading_alignment": "center",
        "layout": "none",
        "container_color_scheme": "",
        "color_scheme": "",
        "open_first_collapsible_row": true,
        "image_ratio": "medium",
        "desktop_layout": "image_with_content",
        "padding_top_desktop": 80,
        "padding_bottom_desktop": 80,
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0
      }
    },
    "b197a95b-81af-4054-bd85-1510d822e192": {
      "type": "rich-text",
      "blocks": {
        "b5af394f-15ee-4ff5-9662-dda791410a61": {
          "type": "custom_liquid",
          "settings": {
            "title": "My custom liquid",
            "custom_liquid": "<h1>WHY DOWNSHIFT EMPHASIZES <span class=\"bon-highlight-color--light\">TERPENES</span></h1>"
          }
        }
      },
      "block_order": [
        "b5af394f-15ee-4ff5-9662-dda791410a61"
      ],
      "custom_css": [
        ".rich-text__wrapper {justify-content: flex-start;}"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "left",
        "color_scheme": "background-2",
        "padding_top_mobile": 80,
        "padding_top": 0,
        "padding_bottom_mobile": 0,
        "padding_bottom": 20,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "background-2",
        "content_width": "page-width custom-width",
        "content_max_width_tablet": 550,
        "content_max_width_desktop": 1000,
        "mobile_content_alignment": "left",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": ""
      }
    },
    "9c2c3a72-3ec1-43fc-92db-08bf0d1fe884": {
      "type": "image-with-text",
      "blocks": {
        "173d0d99-657b-4a5c-acc0-78085ccbb7f4": {
          "type": "heading",
          "settings": {
            "heading": "TERPENES AND SCIENCE",
            "heading_liquid": "",
            "heading_size": "h3"
          }
        },
        "7a06dc3c-6ddb-4b43-bf0f-486617147b44": {
          "type": "text",
          "settings": {
            "text": "<p>At DownShift, we're not just crafting beverages; we're curating experiences. By harnessing the intrinsic benefits of terpenes, we offer more than just hydration — we provide a conduit to enhanced wellness and natural harmony.</p>",
            "text_style": "subtitle"
          }
        }
      },
      "block_order": [
        "173d0d99-657b-4a5c-acc0-78085ccbb7f4",
        "7a06dc3c-6ddb-4b43-bf0f-486617147b44"
      ],
      "settings": {
        "image": "shopify://shop_images/terpenes_3.jpg",
        "show_video_controls": false,
        "image_contain": false,
        "height": "small",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "adapt",
        "desktop_image_width": "medium",
        "layout": "image_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "background-2",
        "image_behavior": "none",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 80,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 80,
        "padding_bottom_desktop": 80,
        "page": "",
        "hide_size": "",
        "color_scheme_section": "background-2",
        "content_width": "page-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "section--image-with-text--custom",
        "custom_liquid": ""
      }
    },
    "028535e4-071f-44ff-80fb-ead89483afcd": {
      "type": "image-with-text",
      "blocks": {
        "f981f133-78d6-4bf4-b7a9-8201d1b889f4": {
          "type": "heading",
          "settings": {
            "heading": "OUR COMMITMENT TO WELL-BEING",
            "heading_liquid": "",
            "heading_size": "h3"
          }
        },
        "1ba2c01c-aa2e-445a-a0ca-83d38a77788a": {
          "type": "text",
          "settings": {
            "text": "<p>Grounded in Research: Our commitment to quality is matched by our dedication to science. We continually explore the latest research to ensure that our terpene profiles are both effective and enjoyable.</p>",
            "text_style": "subtitle"
          }
        }
      },
      "block_order": [
        "f981f133-78d6-4bf4-b7a9-8201d1b889f4",
        "1ba2c01c-aa2e-445a-a0ca-83d38a77788a"
      ],
      "settings": {
        "image": "shopify://shop_images/terpenese_4.jpg",
        "show_video_controls": false,
        "image_contain": false,
        "height": "small",
        "show_video_mobile_controls": false,
        "image_mobile_contain": false,
        "height_mobile": "adapt",
        "desktop_image_width": "medium",
        "layout": "text_first",
        "desktop_content_position": "middle",
        "desktop_content_alignment": "left",
        "content_layout": "no-overlap",
        "color_scheme_content": "background-2",
        "image_behavior": "none",
        "mobile_content_alignment": "left",
        "padding_top_mobile": 40,
        "padding_bottom_mobile": 40,
        "padding_top_desktop": 80,
        "padding_bottom_desktop": 140,
        "page": "",
        "hide_size": "",
        "color_scheme_section": "background-2",
        "content_width": "page-width",
        "content_position": "full-width",
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "content_padding_mobile": 1.5,
        "content_padding_desktop": 4,
        "custom_css_class": "section--image-with-text--custom",
        "custom_liquid": ""
      }
    },
    "32525b6e-7fd9-40f6-8b93-43097da0b7f0": {
      "type": "rich-text",
      "blocks": {
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "LEARN MORE ABOUT TERPENES",
            "heading_size": "h3",
            "text_indent": ""
          }
        },
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1": {
          "type": "text",
          "settings": {
            "text": "<p>Researchers continue to discover the incredible benefits terpenes have on the human body and mind.</p>",
            "text_indent": ""
          }
        }
      },
      "block_order": [
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-heading-1",
        "template--21493599568188__d8666e20-a71f-40e1-b978-644d8ec4f4b7-text-1"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "left",
        "color_scheme": "background-1",
        "padding_top_mobile": 80,
        "padding_top": 0,
        "padding_bottom_mobile": 30,
        "padding_bottom": 10,
        "page": "",
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "left",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_css_class": "",
        "custom_liquid": "<style>\n@media screen and (min-width: 750px) {\n#shopify-section-{{ section.id }} .rich-text__blocks {\n  display: grid;\n  grid-template-columns: 50% auto;\n  align-items: top;\n  justify-items: flex-start;\n}\n#shopify-section-{{ section.id }} .rich-text__blocks .rich-text__text {\n  margin-top: .5rem;\n}}\n</style>"
      }
    },
    "5d3a9773-a400-42cb-bc37-8e4fc09c0f14": {
      "type": "multicolumn",
      "blocks": {
        "template--16993423655157__5d3a9773-a400-42cb-bc37-8e4fc09c0f14-column-1": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/DTS_Daniel_Faro_Everyday_Objects_030_8ba325f7-ceae-407f-82d9-652701e46f93.jpg",
            "title": "",
            "title_liquid": "<div class=\"subtitle--small\">\nYoutube | The Cannabis Doc</div>\n<div class=\"bon-line-height-125\">\nLIMONENE, AKA THE HAPPY TERP</div>",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": true,
            "text_popup": "Watch",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "LIMONENE, AKA THE HAPPY TERP",
            "content_liquid_popup": "<iframe class=\"youtube-player\" width=\"1656\" height=\"869\" src=\"https://www.youtube.com/embed/c6EQ8SuRpPU?enablejsapi=1\" title=\"Limonene AKA The Happy Terp!\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" allowfullscreen></iframe>",
            "color_scheme_popup": "background-2",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "b8916090-4709-4c20-9299-e9c925639bc8": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/DTSyellowstone-3.jpg",
            "title": "",
            "title_liquid": "<div class=\"subtitle--small\">\nYoutube | DeBacco University\n</div>\n<div class=\"bon-line-height-125\">\nTerpene Guide to Pinene</div>",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": true,
            "text_popup": "Watch",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "TERPENE GUIDE TO PINENE",
            "content_liquid_popup": "<iframe class=\"youtube-player\" width=\"1656\" height=\"869\" src=\"https://www.youtube.com/embed/TgCoqaUDXRo&enablejsapi=1\" title=\"Terpene Guide to Pinene\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" allowfullscreen></iframe>",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        },
        "a6dd7275-6cf9-40d3-8b32-59db26309e46": {
          "type": "column",
          "settings": {
            "image": "shopify://shop_images/cloves.jpg",
            "title": "",
            "title_liquid": "<div class=\"subtitle--small\">\nYoutube | Pepper Hernandez\n</div>\n<div class=\"bon-line-height-125\">\nCaryophyllene I Terpene</div>",
            "text": "",
            "text_liquid": "",
            "link_label": "",
            "link_label_liquid": "",
            "link": "",
            "button_style": "link",
            "show_popup": true,
            "text_popup": "Watch",
            "text_liquid_popup": "",
            "button_style_popup": "link underlined-link",
            "page_popup": "",
            "title_popup": "CARYOPHYLLENE I TERPENE",
            "content_liquid_popup": "<iframe class=\"youtube-player\" width=\"1656\" height=\"869\" src=\"https://www.youtube.com/embed/LyQX4O4AaFc?enablejsapi=1\" title=\"Caryophyllene I Terpene I Dr. Pepper Hernandez\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" allowfullscreen></iframe>",
            "color_scheme_popup": "",
            "colomn_span_amount": "grid__item--span-1",
            "colomn_span_amount_tablet": "grid__item--span-1-tablet",
            "colomn_span_amount_desktop": "grid__item--span-1-desktop"
          }
        }
      },
      "block_order": [
        "template--16993423655157__5d3a9773-a400-42cb-bc37-8e4fc09c0f14-column-1",
        "b8916090-4709-4c20-9299-e9c925639bc8",
        "a6dd7275-6cf9-40d3-8b32-59db26309e46"
      ],
      "custom_css": [
        "slider-component {padding: 0;}",
        ".multicolumn-card__info {padding-left: 0; padding-right: 0;}",
        ".multicolumn-card__image-wrapper {margin-left: 0; margin-right: 0;}"
      ],
      "settings": {
        "hide_size": "",
        "color_scheme_section": "background-1",
        "color_scheme_content": "background-1",
        "background_style": "none",
        "content_width": "page-width",
        "content_position": "full-width",
        "columns_tablet": 2,
        "columns_desktop": 3,
        "columns_mobile": "1",
        "swipe_on_mobile": false,
        "swipe_on_desktop": false,
        "image_width": "full",
        "image_ratio": "adapt",
        "column_alignment_mobile": "left",
        "column_alignment_desktop": "left",
        "heading_size": "h6",
        "button_label": "",
        "button_label_liquid": "",
        "button_link": "",
        "button_style": "button button--primary",
        "padding_top_mobile": 10,
        "padding_bottom_mobile": 55,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 80,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "link_image": true,
        "page": "",
        "custom_css_class": "",
        "custom_liquid": "<script>\n  function pauseVideos() {\n    const iframeVideos = document.querySelectorAll('iframe');\n    for (const iframe of iframeVideos) {\n      if (iframe.src.includes('youtube.com')) {\n        iframe.contentWindow.postMessage('{\"event\":\"command\",\"func\":\"pauseVideo\",\"args\":\"\"}', '*');\n      }\n    }\n  }\n\n  // Attach click event listeners to elements with class product-popup-modal__toggle\n  const toggleElements = document.querySelectorAll('.product-popup-modal__toggle');\n  toggleElements.forEach(function (element) {\n    element.addEventListener('click', function () {\n      console.log('clicked button')\n      pauseVideos(); // Call pauseVideos function when any toggle element is clicked\n    });\n  });\n</script>"
      }
    }
  },
  "order": [
    "1be5424f-c0ff-4f8b-a3f3-21054757489c",
    "main",
    "8eeadbf9-c0f4-40a7-83d9-31f6cdd08cc8",
    "d2898378-8989-46cb-8fbd-a011bfefef7b",
    "f20ce790-27ba-442c-ba02-31935dddb131",
    "f9ae08e7-85ec-48ba-b365-d3a6e2335aa9",
    "9be79833-3a8d-45fa-b207-03d30d44cdfa",
    "b197a95b-81af-4054-bd85-1510d822e192",
    "9c2c3a72-3ec1-43fc-92db-08bf0d1fe884",
    "028535e4-071f-44ff-80fb-ead89483afcd",
    "32525b6e-7fd9-40f6-8b93-43097da0b7f0",
    "5d3a9773-a400-42cb-bc37-8e4fc09c0f14"
  ]
}
