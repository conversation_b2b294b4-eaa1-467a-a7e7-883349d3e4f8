{% comment %}
  bs-add
  - section modified to allow marquee, and more control over options for bar
{% endcomment %}

{%- if section.settings.bar_behavior != 'marquee' -%}
  {{ 'component-slideshow.css' | asset_url | stylesheet_tag }}
  {{ 'component-slider.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}

  .section-{{ section.id }}-padding .marquee-container {
    --gap: {{ section.settings.marquee_gap_size_mobile | default: 1 }}rem;
    --duration: {{ section.settings.marquee_speed_mobile | default: 60 }}s;
  }
  .section-{{ section.id }}-padding .announcement-bar__image__container {
    max-width: min(100%, {{ section.settings.marquee_image_width_mobile }}px);
  }
  .section-{{ section.id }}-padding .marquee .media > .announcement-bar__image {
    width: {{ section.settings.marquee_image_width_mobile }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding .marquee-container {
      --gap: {{ section.settings.marquee_gap_size | default: 1 }}rem;
      --duration: {{ section.settings.marquee_speed | default: 60 }}s;
    }
    .section-{{ section.id }}-padding .announcement-bar__image__container {
      max-width: min(100%, {{ section.settings.marquee_image_width }}px);
    }
    .section-{{ section.id }}-padding .marquee .media > .announcement-bar__image {
      width: {{ section.settings.marquee_image_width }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign social_icons = true
  if settings.social_facebook_link == blank and settings.social_instagram_link == blank and settings.social_youtube_link == blank and settings.social_tiktok_link == blank and settings.social_twitter_link == blank and settings.social_pinterest_link == blank and settings.social_snapchat_link == blank and settings.social_tumblr_link == blank and settings.social_vimeo_link == blank
    assign social_icons = false
  endif
  if section.settings.enable_country_selector or section.settings.enable_language_selector
    assign language_country_selector = true
  endif
  if section.blocks.size > 0
    assign announcement_bar = true
  endif
-%}

{% if social_icons %}
  {{ 'component-list-social.css' | asset_url | stylesheet_tag }}
{% endif %}

<div class="{{ section.settings.custom_css_class }}">
  <div class="utility-bar color-{{ section.settings.color_scheme }} gradient utility-bar--{{ section.settings.border }}-border{% if section.settings.enable_country_selector or section.settings.enable_language_selector %} header-localization{% endif %}">
    <div class="section-{{ section.id }}-padding">
      <div class="{{ section.settings.content_width }} utility-bar__grid{% if announcement_bar and language_country_selector or section.settings.show_social and social_icons %} utility-bar__grid--3-col{% elsif language_country_selector or section.settings.show_social and social_icons %} utility-bar__grid--2-col{% endif %}">
        {%- if section.settings.show_social and social_icons -%}
          {%- render 'social-icons' -%}
        {%- endif -%}
        {%- if section.blocks.size == 1 and section.blocks.first.type == 'announcement' -%}
          <div
            class="announcement-bar{% if section.settings.show_social %} announcement-bar--one-announcement{% endif %}"
            role="region"
            aria-label="{{ 'sections.header.announcement' | t }}"
            {{ section.blocks.first.shopify_attributes }}
          >
            {%- assign text_content = section.blocks.first.settings.text_liquid | default: section.blocks.first.settings.text -%}
            {%- if text_content != blank -%}
              <p class="announcement-bar__message {{ section.blocks.first.settings.font_size }}">
                {% assign text_link_classes = '<a class="link link--text ' | append: section.blocks.first.settings.font_size | append: '"' %}
                <span>{{ text_content | replace: '<a ', text_link_classes }}</span>
              </p>
            {%- endif -%}
          </div>
        {%- elsif section.blocks.size > 1 -%}
          {%- if section.settings.bar_behavior != 'marquee' and section.blocks.first.type == 'announcement' -%}
            <slideshow-component
              class="announcement-bar"
              role="region"
              aria-roledescription="{{ 'sections.announcements.carousel' | t }}"
              aria-label="{{ 'sections.announcements.announcement_bar' | t }}"
            >
              <div class="announcement-bar-slider slider-buttons">
                <button
                  type="button"
                  class="slider-button slider-button--prev"
                  name="previous"
                  aria-label="{{ 'sections.announcements.previous_announcement' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  <span class="svg-wrapper">
                    {{- 'icon-caret.svg' | inline_asset_content -}}
                  </span>
                </button>
                <div
                  class="grid grid--1-col slider slider--everywhere"
                  id="Slider-{{ section.id }}"
                  aria-live="polite"
                  aria-atomic="true"
                  data-autoplay="{{ section.settings.auto_rotate }}"
                  data-speed="{{ section.settings.change_slides_speed }}"
                >
                  {%- for block in section.blocks -%}
                    <div
                      class="slideshow__slide slider__slide grid__item grid--1-col"
                      id="Slide-{{ section.id }}-{{ forloop.index }}"
                      {{ block.shopify_attributes }}
                      role="group"
                      aria-roledescription="{{ 'sections.announcements.announcement' | t }}"
                      aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
                      tabindex="-1"
                    >
                      <div
                        class="announcement-bar__announcement"
                        role="region"
                        aria-label="{{ 'sections.header.announcement' | t }}"
                      >
                        {% case block.type %}
                          {%- when 'announcement' -%}
                            {%- assign text_content = block.settings.text_liquid | default: block.settings.text -%}
                            {%- if text_content != blank -%}
                              <p class="announcement-bar__message {{ block.settings.font_size }}">
                                {% assign text_link_classes = '<a class="link link--text ' | append: block.settings.font_size | append: '"' %}
                                <span>{{ text_content | replace: '<a ', text_link_classes }}</span>
                              </p>
                            {%- endif -%}
                        {%- endcase -%}
                      </div>
                    </div>
                  {%- endfor -%}
                </div>
                <button
                  type="button"
                  class="slider-button slider-button--next"
                  name="next"
                  aria-label="{{ 'sections.announcements.next_announcement' | t }}"
                  aria-controls="Slider-{{ section.id }}"
                >
                  <span class="svg-wrapper">
                    {{- 'icon-caret.svg' | inline_asset_content -}}
                  </span>
                </button>
              </div>
            </slideshow-component>
            {%- if request.design_mode -%}
              <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
              <style>
                /* theme editor power preview fix */
                .announcement-bar-slider .slider__slide[aria-hidden='true'] {
                  visibility: hidden;
                }
              </style>
            {%- endif -%}
          {%- else -%}
            <div class="marquee-container">
              <div class="marquee">
                {%- liquid
                  assign number_of_columns = 5
                  assign number_of_columns_mobile = 2
                  assign grid_space_desktop = number_of_columns | minus: 1 | times: settings.spacing_grid_horizontal | plus: 100 | append: 'px'
                  assign grid_space_tablet = number_of_columns_mobile | minus: 1 | times: settings.spacing_grid_horizontal | plus: 100 | append: 'px'
                  assign grid_space_mobile = number_of_columns_mobile | minus: 1 | times: settings.spacing_grid_horizontal | divided_by: 2 | plus: 30 | append: 'px'
                  assign image_width = 1
                -%}
                {% capture sizes %}
                  (min-width: {{ settings.page_width }}px) calc(({{ settings.page_width }}px - {{ grid_space_desktop }}) * {{ image_width }} /  {{ number_of_columns }}),
                  (min-width: 990px) calc((100vw - {{ grid_space_desktop }}) * {{ image_width }} / {{ number_of_columns }}),
                  (min-width: 750px) calc((100vw - {{ grid_space_tablet }}) * {{ image_width }} / {{ number_of_columns_mobile }}),
                  calc((100vw - {{ grid_space_mobile }}) * {{ image_width }} / {{ number_of_columns_mobile }})
                {% endcapture %}
                {%- capture annoucement_content -%}
                  {%- for block in section.blocks -%}
                    <div
                      class="announcement-bar__announcement"
                      role="region"
                      aria-label="{{ 'sections.header.announcement' | t }}"
                      {{ block.shopify_attributes }}
                    >
                      {% case block.type %}
                        {%- when 'announcement' -%}
                          {%- assign text_content = block.settings.text_liquid | default: block.settings.text -%}
                          {%- if text_content != blank -%}
                            <p class="announcement-bar__message {{ section.settings.font_size }}">
                              {% assign text_link_classes = '<a class="link link--text ' | append: section.settings.font_size | append: '"' %}
                              <span>{{ text_content | replace: '<a ', text_link_classes }}</span>
                            </p>
                          {%- endif -%}
                        {%- when 'announcement_media' -%}
                          {%- if block.settings.link != blank -%}
                            {%- comment -%} Keep on one line for replace part below for ARIA {%- endcomment -%}
                            <a href="{{ block.settings.link }}" class="announcement-bar__link link" aria-label="{{ block.settings.image.alt }} image link">
                          {%- endif -%}
                            <span class="announcement-bar__image__container">
                              <span
                                class="media media--transparent"
                              >
                                {%- if block.settings.image != blank -%}
                                  {{
                                    block.settings.image
                                    | image_url: width: 3200
                                    | image_tag:
                                      widths: '50, 75, 100, 150, 200, 300, 400, 500, 750, 1000, 1250, 1500, 1750, 2000, 2250, 2500, 2750, 3000, 3200',
                                      sizes: sizes,
                                      class: 'announcement-bar__image'
                                  }}
                                {%- else -%}
                                  {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder announcement-bar__image' }}
                                {%- endif -%}
                              </span>
                            </span>
                          {%- if block.settings.link != blank -%}
                            </a>
                          {%- endif -%}
                      {%- endcase -%}
                    </div>
                  {%- endfor -%}
                {%- endcapture -%}
  
                <div class="marquee-words">
                  {{ annoucement_content }}
                </div>
                <div class="marquee-words" aria-hidden="true">
                  {%- comment -%} Find and replace needed for ARIA {%- endcomment -%}
                  {{ annoucement_content | replace: '<a ', '<a tabindex="-1" ' }}
                </div>
                <div class="marquee-words" aria-hidden="true">
                  {%- comment -%} Find and replace needed for ARIA {%- endcomment -%}
                  {{ annoucement_content | replace: '<a ', '<a tabindex="-1" ' }}
                </div>
              </div>
            </div>
          {%- endif -%}
          <div class="localization-wrapper">
            {%- if section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
              <localization-form class="small-hide medium-hide">
                {%- form 'localization', id: 'AnnouncementCountryForm', class: 'localization-form' -%}
                  <div>
                    <h2 class="visually-hidden" id="AnnouncementCountryLabel">{{ 'localization.country_label' | t }}</h2>
                    {%- render 'country-localization', localPosition: 'AnnouncementCountry' -%}
                  </div>
                {%- endform -%}
              </localization-form>
            {% endif %}

            {%- if section.settings.enable_language_selector and localization.available_languages.size > 1 -%}
              <localization-form class="small-hide medium-hide">
                {%- form 'localization', id: 'AnnouncementLanguageForm', class: 'localization-form' -%}
                  <div>
                    <h2 class="visually-hidden" id="AnnouncementLanguageLabel">{{ 'localization.language_label' | t }}</h2>
                    {%- render 'language-localization', localPosition: 'AnnouncementLanguage' -%}
                  </div>
                {%- endform -%}
              </localization-form>
            {%- endif -%}
          </div>
        {%- endif -%}
      </div>
    </div>
  </div>
</div>

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.announcement-bar.name",
  "class": "announcement-bar-section",
  "settings": [
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-4"
    },
    {
      "type": "select",
      "id": "border",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        },
        {
          "value": "both",
          "label": "Both"
        }
      ],
      "default": "",
      "label": "Border"
    },
    {
      "type": "select",
      "id": "content_width",
      "options": [
        {
          "value": "full-width",
          "label": "Full width on all devices"
        },
        {
          "value": "page-width",
          "label": "Page width (based on theme settings)"
        }
      ],
      "default": "page-width",
      "label": "Section width"
    },
    {
      "type": "select",
      "id": "bar_behavior",
      "options": [
        {
          "value": "slideshow",
          "label": "Slideshow"
        },
        {
          "value": "marquee",
          "label": "Marquee"
        }
      ],
      "default": "slideshow",
      "label": "Bar behavior",
      "info": "Valid if more than one block is added."
    },
    {
      "type": "header",
      "content": "Marquee options"
    },
    {
      "type": "range",
      "id": "marquee_gap_size",
      "min": 0,
      "max": 10,
      "step": 1,
      "default": 1,
      "label": "Marquee gap size",
      "info": "Space between items"
    },
    {
      "type": "range",
      "id": "marquee_gap_size_mobile",
      "min": 0,
      "max": 10,
      "step": 1,
      "default": 1,
      "label": "Marquee gap size (mobile)",
      "info": "Space between items"
    },
    {
      "type": "range",
      "id": "marquee_speed",
      "min": 5,
      "max": 100,
      "step": 5,
      "default": 60,
      "label": "Marquee speed",
      "info": "Lower is faster"
    },
    {
      "type": "range",
      "id": "marquee_speed_mobile",
      "min": 5,
      "max": 100,
      "step": 5,
      "default": 60,
      "label": "Marquee speed (mobile)",
      "info": "Lower is faster"
    },
    {
      "type": "range",
      "id": "marquee_image_width",
      "min": 50,
      "max": 550,
      "step": 5,
      "unit": "px",
      "label": "Marquee image width",
      "default": 100
    },
    {
      "type": "range",
      "id": "marquee_image_width_mobile",
      "min": 50,
      "max": 550,
      "step": 5,
      "unit": "px",
      "label": "Marquee image width (mobile)",
      "default": 100
    },
    {
      "type": "header",
      "content": "Slideshow options"
    },
    {
      "type": "checkbox",
      "id": "auto_rotate",
      "label": "t:sections.announcement-bar.settings.auto_rotate.label",
      "default": false
    },
    {
      "type": "range",
      "id": "change_slides_speed",
      "min": 3,
      "max": 10,
      "step": 1,
      "unit": "s",
      "label": "t:sections.announcement-bar.settings.change_slides_speed.label",
      "default": 5
    },
    {
      "type": "header",
      "content": "t:sections.announcement-bar.settings.heading_utilities.content"
    },
    {
      "type": "paragraph",
         "content": "t:sections.announcement-bar.settings.paragraph.content"
    },             
    {
      "type": "checkbox",
      "id": "show_social",
      "default": false,
      "label": "t:sections.announcement-bar.settings.show_social.label",
      "info": "t:sections.announcement-bar.settings.show_social.info"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": false,
      "label": "t:sections.announcement-bar.settings.enable_country_selector.label",
      "info": "t:sections.announcement-bar.settings.enable_country_selector.info"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": false,
      "label": "t:sections.announcement-bar.settings.enable_language_selector.label",
      "info": "t:sections.announcement-bar.settings.enable_language_selector.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading_desktop"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 400,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top_desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 400,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom_desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading_mobile"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 400,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top_mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 400,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom_mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Advanced settings (optional)"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    }
  ],
  "blocks": [
    {
      "type": "announcement",
      "name": "t:sections.announcement-bar.blocks.announcement.name",
      "settings": [
        {
          "type": "inline_richtext",
          "id": "text",
          "default": "t:sections.announcement-bar.blocks.announcement.settings.text.default",
          "label": "Text"
        },
        {
          "type": "liquid",
          "id": "text_liquid",
          "label": "Text liquid",
          "info": "Overrides Text above"
        },
        {
          "type": "select",
          "id": "font_size",
          "options": [
            {
              "value": "text-body",
              "label": ".text-body"
            },
            {
              "value": "h6",
              "label": "t:sections.all.heading_size.options__h6.label"
            },
            {
              "value": "h5",
              "label": "t:sections.all.heading_size.options__h5.label"
            },
            {
              "value": "h4",
              "label": "t:sections.all.heading_size.options__h4.label"
            },
            {
              "value": "h3",
              "label": "t:sections.all.heading_size.options__h3.label"
            },
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            }
          ],
          "default": "h5",
          "label": "Font size"
        }
      ]
    },
    {
      "type": "announcement_media",
      "name": "Announcement Media",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.announcement-bar.blocks.announcement.settings.link.label"
        }
      ]
    },
  ],
  "presets": [
    {
      "name": "t:sections.announcement-bar.presets.name",
      "blocks": [
        {
          "type": "announcement"
        }
      ]
    }
  ]
}
{% endschema %}