{% comment %}
  bs-mod
  - hide options added
{% endcomment %}

{%- style -%}
  {% render 'section-hide-css-logic' %}
  {% render 'section-padding-css-logic' %}
{%- endstyle -%}

<div class="{{ section.settings.custom_css_class }}">
  <div class="color-{{ section.settings.color_scheme }} gradient">
    <div class="section-{{ section.id }}-padding">
      <div class="{% if section.settings.include_margins %}page-width{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
        {%- for block in section.blocks -%}
          {% render block %}
        {%- endfor -%}
      </div>
    </div>
  </div>
</div>

{{ section.settings.custom_liquid }}

{% schema %}
{
  "name": "t:sections.apps.name",
  "tag": "section",
  "class": "section section--app",
  "settings": [
    {
      "type": "checkbox",
      "id": "include_margins",
      "default": true,
      "label": "t:sections.apps.settings.include_margins.label"
    },
    {
      "type": "header",
      "content": "Hide options"
    },
    {
      "type": "select",
      "id": "hide_size",
      "options": [
        {
          "value": "",
          "label": "None"
        },
        {
          "value": "small-hide",
          "label": "Small (<750px)"
        },
        {
          "value": "small-hide medium-hide",
          "label": "Small and Medium (<990px)"
        },
        {
          "value": "medium-hide",
          "label": "Medium (>749px and <990px)"
        },
        {
          "value": "medium-hide large-up-hide",
          "label": "Medium and Large (>749px)"
        },
        {
          "value": "large-up-hide",
          "label": "Large (>989px)"
        }
      ],
      "default": "",
      "label": "Hide on screen size"
    },
    {
      "type": "header",
      "content": "Color Scheme"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "Content padding mobile (max-width: 749px)"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Padding top mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 300,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_left_mobile",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding left mobile",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_mobile",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding right mobile",
      "default": 0
    },
    {
      "type": "header",
      "content": "Content padding desktop (min-width: 750px)"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 500,
      "step": 5,
      "unit": "px",
      "label": "Padding top desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 500,
      "step": 5,
      "unit": "px",
      "label": "Padding bottom desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_left_desktop",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding left desktop",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_right_desktop",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Padding right desktop",
      "default": 0
    },
    {
      "type": "header",
      "content": "Custom overrides"
    },
    {
      "type": "paragraph",
      "content": "Use this for section id: #shopify-section-{{ section.id }}"
    },
    {
      "type": "text",
      "id": "custom_css_class",
      "label": "Custom class names"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "t:sections.apps.presets.name"
    }
  ]
}
{% endschema %}
