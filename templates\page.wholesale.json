/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-page",
      "disabled": true,
      "custom_css": [

      ],
      "settings": {
        "heading_size": "h1",
        "padding_top_mobile": 36,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 56,
        "padding_bottom_desktop": 56
      }
    },
    "42631026-c91f-4bed-8481-eaa6f0fa10cb": {
      "type": "rich-text",
      "blocks": {
        "template--17029290066165__42631026-c91f-4bed-8481-eaa6f0fa10cb-heading-1": {
          "type": "heading",
          "settings": {
            "heading": "BECOME A WHOLESALER",
            "heading_size": "h1",
            "text_indent": ""
          }
        },
        "template--17029290066165__42631026-c91f-4bed-8481-eaa6f0fa10cb-text-1": {
          "type": "text",
          "settings": {
            "text": "<p>Welcome to the Wholesale Inquiries page of Shift Naturals Inc., home of innovative and natural wellness products. We are thrilled to see your interest in partnering with us to distribute our range of nature-inspired products. Our commitment to quality, innovation, and the purity of natural ingredients is at the heart of everything we do, and we are excited to share this journey with you.<\/p><p><strong>Why Partner with Us?<\/strong><\/p><ul><li><strong>Quality Products:<\/strong> At Shift Naturals Inc., we pride ourselves on crafting products that harness the power of nature. Our range includes a variety of offerings, from energizing to calming solutions, all made with unadulterated, plant-derived compounds.<\/li><li><strong>Innovative Solutions<\/strong>: We are constantly exploring new frontiers in natural wellness. Our research and development team works tirelessly to bring the latest in natural health to our product lines.<\/li><li><strong>Support and Growth<\/strong>: We believe in growing together. Our partners receive comprehensive support, including marketing materials, product training, and dedicated account management, to ensure mutual growth and success.<\/li><\/ul><p><strong>How to Become a Wholesale Partner:<\/strong><\/p><p>Becoming a wholesale partner with Shift Naturals Inc. is a straightforward process. Here’s how you can get started:<\/p><ol><li><strong>Inquiry Submission<\/strong>: Fill out our online inquiry form with details about your business and how you plan to market and distribute our products. The more information you provide, the better we can tailor our partnership to fit your needs.<\/li><li><strong>Review Process<\/strong>: Our team will review your submission and get in touch to discuss potential partnership opportunities. We may ask for additional information or set up a meeting to understand your business better.<\/li><li><strong>Partnership Agreement<\/strong>: Once we decide to move forward, we'll provide you with a wholesale partnership agreement outlining the terms, conditions, and benefits of our collaboration.<\/li><li><strong>Onboarding & Training<\/strong>: After finalizing the agreement, we will onboard you to our systems, provide product training, and equip you with all necessary marketing and sales materials.<\/li><\/ol><p><strong>Contact Us for More Information<\/strong>:<\/p><p>If you have any questions or need further information, please don’t hesitate to contact <NAME_EMAIL>. Our team is ready to assist you and provide the necessary guidance to kickstart our partnership.<\/p><p>Ready to #ShiftWithNature?<\/p>",
            "text_indent": ""
          }
        }
      },
      "block_order": [
        "template--17029290066165__42631026-c91f-4bed-8481-eaa6f0fa10cb-heading-1",
        "template--17029290066165__42631026-c91f-4bed-8481-eaa6f0fa10cb-text-1"
      ],
      "settings": {
        "desktop_content_position": "full-width",
        "content_alignment": "left",
        "color_scheme": "scheme-1",
        "padding_top_mobile": 20,
        "padding_top": 40,
        "padding_bottom_mobile": 20,
        "padding_bottom": 40,
        "hide_size": "",
        "show_color_scheme_content": false,
        "color_scheme_content": "",
        "content_width": "page-width page-width--narrow",
        "content_max_width_tablet": 900,
        "content_max_width_desktop": 1600,
        "mobile_content_alignment": "",
        "padding_left_mobile": 0,
        "padding_left_desktop": 0,
        "padding_right_mobile": 0,
        "padding_right_desktop": 0,
        "custom_liquid": ""
      }
    },
    "db2a8c6c-77ec-4811-8fe9-d84f33b033fe": {
      "type": "contact-form",
      "disabled": true,
      "settings": {
        "heading": "Contact Form",
        "heading_liquid": "",
        "heading_size": "h2",
        "color_scheme": "scheme-1",
        "custom_liquid": "<h2 class=\"title title-wrapper--no-top-margin inline-richtext h2 scroll-trigger animate--slide-in\">\n  Business Information\n<\/h2>\n\n<div class=\"field\">\n  <input\n    class=\"field__input\"\n    autocomplete=\"name\"\n    type=\"text\"\n    id=\"ContactForm-name-biz\"\n    name=\"contact[Business Name]\"\n    value=\"{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}\"\n    placeholder=\"Business Name\"\n  >\n  <label class=\"field__label\" for=\"ContactForm-name-biz\">Business Name<\/label>\n<\/div>\n\n<div class=\"contact__fields\">\n  <div class=\"field\">\n    <input\n      class=\"field__input\"\n      autocomplete=\"name\"\n      type=\"text\"\n      id=\"ContactForm-city\"\n      name=\"contact[City]\"\n      value=\"{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}\"\n      placeholder=\"City\"\n    >\n    <label class=\"field__label\" for=\"ContactForm-city\">City<\/label>\n  <\/div>\n  <div class=\"field\">\n    <input\n      class=\"field__input\"\n      autocomplete=\"name\"\n      type=\"text\"\n      id=\"ContactForm-state\"\n      name=\"contact[State]\"\n      value=\"{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}\"\n      placeholder=\"State\"\n    >\n    <label class=\"field__label\" for=\"ContactForm-name\">State<\/label>\n  <\/div>\n<\/div>\n\n<div class=\"field\">\n  <input\n    type=\"tel\"\n    id=\"ContactForm-phone-biz\"\n    class=\"field__input\"\n    autocomplete=\"tel\"\n    name=\"contact[Business Phone]\"\n    pattern=\"[0-9\\-]*\"\n    value=\"{% if form.phone %}{{ form.phone }}{% elsif customer %}{{ customer.phone }}{% endif %}\"\n    placeholder=\"Business Phone\"\n  >\n  <label class=\"field__label\" for=\"ContactForm-phone-biz\">Business Phone<\/label>\n<\/div>\n\n<div class=\"field\">\n  <input\n    class=\"field__input\"\n    autocomplete=\"name\"\n    type=\"text\"\n    id=\"ContactForm-website\"\n    name=\"contact[Website]\"\n    value=\"{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}\"\n    placeholder=\"Website\"\n  >\n  <label class=\"field__label\" for=\"ContactForm-website\">Website<\/label>\n<\/div>\n\n<div class=\"customer addresses\">\n  <div>\n    <label for=\"ContactForm-type-of-bussiness\">\n      Type of Business\n    <\/label>\n    <div class=\"select\">\n      <select\n        id=\"ContactForm-type-of-bussiness\"\n        name=\"contact[type_of_business]\"\n        data-form-id=\"{{ form.id }}\"\n      >\n        <option value=\"retail\">Retail<\/option>\n        <option value=\"distributor\">Distributor<\/option>\n        <option value=\"ecommerce\">E-Commerce<\/option>\n        <option value=\"corporate_office\">Corporate Office<\/option>\n        <option value=\"other\">Other<\/option>\n      <\/select>\n      <svg aria-hidden=\"true\" focusable=\"false\" viewBox=\"0 0 10 6\">\n        <use href=\"#icon-caret\" \/>\n      <\/svg>\n    <\/div>\n  <\/div>\n<\/div>\n    \n<div class=\"field field--textarea\">\n  <textarea\n    rows=\"10\"\n    id=\"ContactForm-pref-dist\"\n    class=\"text-area field__input\"\n    name=\"contact[preferred_distributor]\"\n    placeholder=\"If Retail, tell us about your preferred distributor:\"\n  >\n  <\/textarea>\n  <label class=\"form__label field__label\" for=\"ContactForm-pref-dist\">\n    If Retail, tell us about your preferred distributor:\n  <\/label>\n<\/div>",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 36,
        "padding_top_desktop": 20,
        "padding_bottom_desktop": 100,
        "hide_size": ""
      }
    },
    "d7e5b9e0-9a42-4134-a297-157e473d1ed5": {
      "type": "custom-liquid",
      "disabled": true,
      "settings": {
        "description": "",
        "custom_liquid": "<div class=\"page-width--narrow\" style=\"margin: auto;\">\n<iframe src=\"https:\/\/docs.google.com\/forms\/d\/e\/1FAIpQLSdswaoIC8gIAd2AresvqdEaybjyjLPajGWUjeA5b1EE5HHY2w\/viewform?embedded=true\" width=\"640\" height=\"959\" frameborder=\"0\" marginheight=\"0\" marginwidth=\"0\">Loading…<\/iframe><\/div>",
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0
      }
    },
    "170134675002241941": {
      "type": "apps",
      "blocks": {
        "824a9cc4-68fe-42f7-bd66-ba93404b11bb": {
          "type": "shopify:\/\/apps\/forms\/blocks\/inline\/8744a304-fcb1-4347-b211-bb6b4759a76a",
          "settings": {
            "form_id": "48936",
            "text_color": "#2b2d20",
            "button_background_color": "#2b2d20",
            "button_label_color": "#d8dbc8",
            "links_color": "#2b2d20",
            "errors_color": "#e02229",
            "text_alignment": "left",
            "form_alignment": "center",
            "padding_top": 5,
            "padding_bottom": 64,
            "padding_right": 0,
            "padding_left": 0
          }
        }
      },
      "block_order": [
        "824a9cc4-68fe-42f7-bd66-ba93404b11bb"
      ],
      "settings": {
        "include_margins": true,
        "hide_size": "",
        "color_scheme": "",
        "padding_top_mobile": 0,
        "padding_bottom_mobile": 0,
        "padding_left_mobile": 0,
        "padding_right_mobile": 0,
        "padding_top_desktop": 0,
        "padding_bottom_desktop": 0,
        "padding_left_desktop": 0,
        "padding_right_desktop": 0,
        "custom_liquid": "<style>\n#app-embed {\n  --forms-heading-font: var(--font-body-family);\n  --form-field-border-radius: 0;\n    --button-border-radius: 0;\n    --inline-container-max-width: 72.6rem;\n  --form-field-border: 0 0 0 1px rgba(var(--color-foreground), 1);\n  --form-placeholder-color: rgba(var(--color-foreground), 1);\n  --form-background-color: transparent;\n  --button-text-color: rgb(var(--bon-highlight-color--light)); \n}\n<\/style>"
      }
    }
  },
  "order": [
    "main",
    "42631026-c91f-4bed-8481-eaa6f0fa10cb",
    "db2a8c6c-77ec-4811-8fe9-d84f33b033fe",
    "d7e5b9e0-9a42-4134-a297-157e473d1ed5",
    "170134675002241941"
  ]
}
